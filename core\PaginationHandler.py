#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态翻页处理器 (PaginationHandler)

=== 模块概述 ===
动态翻页处理器是爬虫系统的核心翻页模块，负责处理各种复杂的动态翻页场景。
该模块集成了多种翻页策略，提供统一的翻页接口，支持智能检测和自动回退。

=== 核心功能 ===
1. 智能动态翻页处理 - 集成智能检测模块，自动选择最佳策略
2. JSP网站专用处理 - 专门处理JSP技术栈的政府和企业网站
3. 简单点击翻页 - 处理传统的点击式翻页
4. iframe内翻页 - 处理嵌套iframe结构的翻页
5. 滚动翻页 - 处理需要滚动触发的翻页
6. 文章提取集成 - 在翻页过程中同步提取文章信息

=== 模块关系图 ===
PaginationHandler (核心翻页处理器)
├── SmartDynamicHandler (智能动态检测模块)
│   ├── DynamicTypeDetector (网站类型检测)
│   ├── DynamicTypeProcessor (策略处理器)
│   └── 6种处理策略 (JSP, iframe, AJAX, 滚动, React, Vue)
├── PaginationUtils (统一翻页工具)
│   ├── iframe上下文检测
│   ├── 智能翻页策略
│   └── JavaScript翻页支持
├── JSPWebsiteHandler (JSP专用处理器)
│   ├── JSP网站检测
│   ├── iframe内翻页
│   └── goto()函数调用
└── 文章提取器集成
    ├── 页面内容提取
    ├── 链接收集
    └── 数据格式转换

=== 处理策略注释标准 ===
每个翻页处理方法必须遵循以下注释规范：

1. 【处理器标识】: 唯一的处理器名称，格式：HANDLER_[TYPE]_[NAME]
2. 【适用场景】: 详细描述该处理器适用的翻页类型和网站特征
3. 【处理条件】: 明确的处理条件，包括页面特征、技术要求等
4. 【处理流程】: 详细的处理步骤和核心算法
5. 【依赖模块】: 所依赖的其他模块和工具
6. 【成功标准】: 判断处理成功的标准
7. 【失败处理】: 处理失败时的回退机制
8. 【性能考虑】: 性能优化和资源使用注意事项
9. 【维护说明】: 维护和扩展该处理器的注意事项
10. 【测试用例】: 典型的测试场景和预期结果

=== 扩展指南 ===
添加新的翻页处理器时，请遵循以下步骤：
1. 在PaginationHandler类中添加处理方法：handle_[type]_pagination()
2. 添加完整的处理器规则注释
3. 集成相关的依赖模块
4. 实现错误处理和回退机制
5. 编写单元测试和集成测试
6. 更新文档和使用示例

=== 当前支持的处理器类型 ===
- SMART_DYNAMIC: 智能动态翻页处理（优先级：HIGHEST）
- JSP_PAGINATION: JSP网站翻页处理（优先级：HIGH）
- SIMPLE_CLICK: 简单点击翻页处理（优先级：MEDIUM）
- IFRAME_SCROLL: iframe滚动翻页处理（优先级：MEDIUM）
- INFINITE_SCROLL: 无限滚动翻页处理（优先级：LOW）

"""

import asyncio
from playwright.async_api import Page, Browser, BrowserContext
from typing import Optional, Dict, Any, Union, Callable, Awaitable
import logging
import sys

# 导入JSP处理器
try:
    from modules.jsp_website_handler import jsp_handler
    JSP_HANDLER_AVAILABLE = True
except ImportError:
    JSP_HANDLER_AVAILABLE = False
    print("⚠️ JSP处理器不可用")

# 导入统一的翻页工具
try:
    from core.pagination_utils import PaginationUtils
    PAGINATION_UTILS_AVAILABLE = True
except ImportError:
    PAGINATION_UTILS_AVAILABLE = False
    print("⚠️ 翻页工具不可用")

# 导入智能动态检测模块
try:
    from core.dynamic_type_detector import SmartDynamicHandler
    SMART_DYNAMIC_AVAILABLE = True
except ImportError:
    SMART_DYNAMIC_AVAILABLE = False
    print("⚠️ 智能动态检测模块不可用")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('PaginationHandler')

class PaginationHandler:
    async def _detect_pagination_availability(self, next_button_selector, timeout=10000):
        """
        检测页面是否有有效的翻页功能（补充缺失方法）
        :param next_button_selector: 下一页按钮选择器
        :param timeout: 检测超时时间
        :return: True表示有翻页功能，False表示没有
        """
        try:
            logger.info(f"🔍 检测翻页功能，选择器: {next_button_selector}")
            # 检查是否存在下一页按钮
            next_buttons = await self.page.query_selector_all(next_button_selector)
            if not next_buttons:
                logger.info("❌ 未找到下一页按钮")
                return False
            logger.info(f"✅ 找到 {len(next_buttons)} 个匹配的元素")
            # 检查按钮是否可见和可用
            for i, button in enumerate(next_buttons):
                try:
                    is_visible = await button.is_visible()
                    is_enabled = await button.is_enabled()
                    text = await button.text_content()
                    href = await button.get_attribute('href')
                    onclick = await button.get_attribute('onclick')
                    logger.info(f"  按钮 {i+1}: 可见={is_visible}, 可用={is_enabled}, 文本='{text}', href='{href}', onclick='{onclick}'")
                    if is_visible and is_enabled:
                        return True
                except Exception as e:
                    logger.warning(f"  检查按钮 {i+1} 时出错: {e}")
            logger.info("❌ 没有找到可用的翻页按钮")
            return False
        except Exception as e:
            logger.error(f"翻页功能检测失败: {e}")
            return False
    """翻页处理器，支持多种动态翻页方式"""

    def __init__(self, page: Page):
        self.page = page
        self.all_articles = []  # 存储所有收集到的文章
        self.current_url = None  # 当前页面URL

    async def debug_pagination_elements(self, selectors: list = None) -> dict:
        """
        调试翻页元素，检查页面上可能的翻页按钮
        :param selectors: 要检查的选择器列表，如果为None则使用默认列表
        :return: 调试信息字典
        """
        if selectors is None:
            selectors = [
                "a.next:not(.lose)",
                "a.page-link:has-text('Next')",
                "a[onclick*='page']",
                ".js_pageI:not(.cur)",
                ".pager a",
                "a:contains('下一页')",
                "a:contains('Next')",
                ".next-page"
            ]

        debug_info = {
            'found_elements': [],
            'page_url': self.page.url,
            'page_title': await self.page.title()
        }

        for selector in selectors:
            try:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    for i, element in enumerate(elements):
                        text = await element.text_content()
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()
                        debug_info['found_elements'].append({
                            'selector': selector,
                            'index': i,
                            'text': text.strip() if text else '',
                            'visible': is_visible,
                            'enabled': is_enabled
                        })
            except Exception as e:
                logger.warning(f"Error checking selector {selector}: {str(e)}")

        return debug_info

    async def handle_jsp_pagination(
        self,
        max_pages: int = 5,
        start_page: int = 1,
        log_callback: Optional[Callable] = None
    ) -> int:
        """
        【处理器标识】: HANDLER_JSP_PAGINATION
        【适用场景】: JSP技术栈的政府网站、企业网站，特别是使用iframe结构的传统Web应用
        【处理条件】:
            - JSP处理器模块可用
            - URL包含.jsp扩展名或检测到JSP特征
            - 页面包含iframe结构或JavaScript翻页函数
        【处理流程】:
            1. 检查JSP处理器可用性
            2. 验证URL是否支持JSP处理
            3. 调用JSP处理器进行翻页和链接收集
            4. 将收集到的文章链接转换为标准元组格式
            5. 存储到all_articles列表中
        【依赖模块】:
            - modules.jsp_website_handler.jsp_handler (JSP专用处理器)
            - core.pagination_utils.PaginationUtils (统一翻页工具)
        【成功标准】: 成功收集到文章链接并转换为标准格式
        【失败处理】: 记录错误日志，返回0表示处理失败
        【性能考虑】:
            - JSP网站通常需要较长的页面加载时间
            - iframe内容加载需要额外等待时间
            - JavaScript函数调用需要适当的延迟
        【维护说明】:
            - JSP网站结构相对稳定，主要关注JavaScript函数名变化
            - 需要定期检查iframe结构的变化
            - 注意处理不同JSP框架的差异
        【测试用例】:
            - 合肥政协网站: http://www.hfszx.org.cn/hfzx/web/list.jsp
            - 预期结果: 成功处理多页，收集文章链接，使用goto()函数翻页

        Args:
            max_pages: 最大页数
            start_page: 起始页数
            log_callback: 日志回调函数

        Returns:
            int: 实际处理的页数
        """
        if not JSP_HANDLER_AVAILABLE:
            if log_callback:
                log_callback("❌ JSP处理器不可用")
            else:
                logger.error("JSP处理器不可用")
            return 0

        # 获取当前页面URL
        current_url = self.page.url
        self.current_url = current_url

        if log_callback:
            log_callback(f"🎯 开始JSP动态翻页处理: {current_url}")
        else:
            logger.info(f"开始JSP动态翻页处理: {current_url}")

        # 检查URL是否支持JSP处理器
        if not jsp_handler.is_supported_url(current_url):
            if log_callback:
                log_callback(f"⚠️ URL不支持JSP处理器: {current_url}")
            else:
                logger.warning(f"URL不支持JSP处理器: {current_url}")
            return 0

        try:
            # 使用JSP处理器进行翻页和文章收集
            article_links = await jsp_handler.handle_pagination(
                page=self.page,
                current_page=start_page,
                max_pages=max_pages
            )

            # 将收集到的文章链接转换为标准格式并存储
            # crawl_articles_async期望的格式是元组: (title, url, save_dir, page_title, page_url, classid)
            for link in article_links:
                article_info = (
                    link['text'],           # title
                    link['href'],           # url
                    "articles",             # save_dir
                    "JSP Articles",         # page_title
                    current_url,            # page_url
                    ""                      # classid
                )
                self.all_articles.append(article_info)

            pages_processed = max_pages if article_links else 0

            if log_callback:
                log_callback(f"✅ JSP翻页完成: 处理{pages_processed}页，收集{len(article_links)}篇文章")
            else:
                logger.info(f"JSP翻页完成: 处理{pages_processed}页，收集{len(article_links)}篇文章")

            return pages_processed

        except Exception as e:
            if log_callback:
                log_callback(f"❌ JSP翻页处理失败: {e}")
            else:
                logger.error(f"JSP翻页处理失败: {e}")
            return 0

    def is_jsp_website(self, url: str = None) -> bool:
        """
        检查是否为JSP网站

        Args:
            url: 要检查的URL，如果为None则使用当前页面URL

        Returns:
            bool: 是否为JSP网站
        """
        if not JSP_HANDLER_AVAILABLE:
            return False

        check_url = url or self.current_url or self.page.url
        return jsp_handler.is_supported_url(check_url)

    async def handle_smart_dynamic_pagination(
        self,
        url: str,
        max_pages: int = 5,
        log_callback: Optional[Callable] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        【处理器标识】: HANDLER_SMART_DYNAMIC_PAGINATION
        【适用场景】: 所有类型的动态网站，作为首选的智能化翻页处理方案
        【处理条件】:
            - 智能动态检测模块可用
            - 页面可正常访问
            - 支持JavaScript执行
        【处理流程】:
            1. 调用SmartDynamicHandler进行网站类型检测
            2. 根据检测结果自动选择最佳处理策略
            3. 执行相应的翻页和内容提取操作
            4. 将结果转换为标准格式并存储
        【依赖模块】:
            - core.dynamic_type_detector.SmartDynamicHandler (智能检测模块)
            - 各种具体的处理策略模块
        【成功标准】: 成功检测网站类型并执行相应策略，收集到文章数据
        【失败处理】: 返回详细错误信息，由上层决定是否回退到其他处理方式
        【性能考虑】:
            - 利用检测缓存避免重复分析
            - 优先级排序减少无效尝试
            - 异步处理提高并发性能
        【维护说明】:
            - 新增网站类型时需要在SmartDynamicHandler中添加相应策略
            - 注意保持与各依赖模块的接口兼容性
        【测试用例】:
            - JSP网站: 合肥政协网站，预期使用JSP_WEBSITE策略
            - iframe网站: 嵌套结构网站，预期使用IFRAME_PAGINATION策略
            - 现代Web应用: React/Vue网站，预期使用相应SPA策略

        Args:
            url: 目标URL
            max_pages: 最大页数
            log_callback: 日志回调函数
            **kwargs: 其他参数

        Returns:
            dict: 处理结果，包含成功状态、策略信息、文章数量等
        """
        if not SMART_DYNAMIC_AVAILABLE:
            if log_callback:
                log_callback("❌ 智能动态检测模块不可用")
            else:
                logger.error("智能动态检测模块不可用")
            return {'success': False, 'error': '智能动态检测模块不可用'}

        try:
            if log_callback:
                log_callback(f"🧠 开始智能动态检测: {url}")
            else:
                logger.info(f"开始智能动态检测: {url}")

            # 创建智能处理器
            smart_handler = SmartDynamicHandler()

            # 执行智能处理
            result = await smart_handler.smart_handle_pagination(
                page=self.page,
                url=url,
                max_pages=max_pages,
                **kwargs
            )

            if result.get('success', False):
                # 将结果转换为标准格式并存储
                articles = result.get('articles', [])
                for article in articles:
                    if isinstance(article, dict):
                        # 转换为标准元组格式
                        article_info = (
                            article.get('text', ''),      # title
                            article.get('href', ''),      # url
                            "articles",                    # save_dir
                            "Smart Dynamic",               # page_title
                            url,                          # page_url
                            ""                            # classid
                        )
                        self.all_articles.append(article_info)

                if log_callback:
                    log_callback(f"✅ 智能动态处理成功: 使用{result.get('used_strategy', 'Unknown')}策略")
                    log_callback(f"   处理页数: {result.get('pages_processed', 0)}")
                    log_callback(f"   收集文章: {len(articles)} 篇")
                else:
                    logger.info(f"智能动态处理成功: 策略={result.get('used_strategy', 'Unknown')}, 文章={len(articles)}篇")

                return {
                    'success': True,
                    'pages_processed': result.get('pages_processed', 0),
                    'articles_collected': len(articles),
                    'strategy_used': result.get('used_strategy', 'Unknown'),
                    'detection_result': result.get('detection_result', {})
                }
            else:
                error_msg = result.get('error', '未知错误')
                if log_callback:
                    log_callback(f"❌ 智能动态处理失败: {error_msg}")
                else:
                    logger.error(f"智能动态处理失败: {error_msg}")

                return {
                    'success': False,
                    'error': error_msg,
                    'detection_result': result.get('detection_result', {})
                }

        except Exception as e:
            error_msg = f"智能动态处理异常: {e}"
            if log_callback:
                log_callback(f"❌ {error_msg}")
            else:
                logger.error(error_msg)

            return {
                'success': False,
                'error': error_msg
            }

    async def click_pagination(
        self,
        next_button_selector: str,
        max_pages: int = 10,
        content_ready_selector: Optional[str] = None,
        timeout: int = 10000,
        wait_after_click: int = 1000,
        disabled_check: bool = True,
        extract_articles_config: dict = None,
        stop_on_duplicate_last_item: bool = True,  # 新增：遇到重复item时提前结束
        auto_detect_pagination: bool = True,  # 新增：自动检测翻页功能
        next_selectors: list = None,  # 新增：可选的下一页选择器列表
        use_simple_pagination: bool = False  # 新增：使用简单翻页模式（参考test_tjszx_pagination.py）
    ) -> int:
        """
        【处理器标识】: HANDLER_CLICK_PAGINATION
        【适用场景】: 传统的点击式翻页网站，包括新闻网站、论坛、博客等使用标准翻页按钮的网站
        【处理条件】:
            - 页面包含可点击的翻页按钮或链接
            - 翻页按钮有明确的CSS选择器
            - 点击后页面内容会发生变化
        【处理流程】:
            1. 检测翻页功能可用性（如果启用auto_detect_pagination）
            2. 循环执行翻页操作直到达到最大页数或无法继续
            3. 每次翻页后等待内容加载完成
            4. 提取当前页面的文章信息（如果配置了extract_articles_config）
            5. 检查重复内容以避免无限循环
        【依赖模块】:
            - core.pagination_utils.PaginationUtils (统一翻页工具，如果可用)
            - Playwright页面操作API
        【成功标准】: 成功点击翻页按钮并检测到页面内容变化
        【失败处理】:
            - 按钮不可点击时停止翻页
            - 页面加载超时时记录警告并继续
            - 遇到重复内容时提前结束（如果启用stop_on_duplicate_last_item）
        【性能考虑】:
            - 合理设置wait_after_click避免过快点击
            - 使用content_ready_selector确保内容加载完成
            - 启用disabled_check避免点击无效按钮
        【维护说明】:
            - 定期检查翻页按钮选择器的有效性
            - 根据网站更新调整等待时间和超时设置
            - 注意处理不同网站的翻页按钮样式变化
        【测试用例】:
            - 标准新闻网站: 使用.next-page或类似选择器
            - 论坛网站: 使用数字翻页或下一页链接
            - 预期结果: 成功翻页并提取每页内容

        Args:
            next_button_selector: 下一页按钮的CSS选择器
            max_pages: 最大翻页次数
            content_ready_selector: 内容加载完成的标识选择器
            timeout: 超时时间(毫秒)
            wait_after_click: 点击后等待的时间（毫秒）
            disabled_check: 是否检查按钮禁用状态
            extract_articles_config: 文章提取配置字典
            stop_on_duplicate_last_item: 遇到重复item时提前结束
        :param auto_detect_pagination: 自动检测翻页功能
        :return: 实际翻页次数
        """
        current_page = 1
        total_articles_extracted = 0
        last_page_last_item_url = None  # 新增：记录上一页最后一个item的url

        # 如果启用简单翻页模式，使用类似test_tjszx_pagination.py的逻辑
        if use_simple_pagination:
            logger.info("🚀 使用简单翻页模式（更稳定的翻页逻辑）")

            # 检查是否是天津税务局等使用URL翻页的网站
            current_url = self.page.url
            if 'tjszx.gov.cn' in current_url and 'index.shtml' in current_url:
                logger.info("🔍 检测到天津税务局网站，使用URL翻页模式")
                return await self._url_based_pagination(
                    max_pages=max_pages,
                    wait_after_click=wait_after_click,
                    extract_articles_config=extract_articles_config
                )
            else:
                return await self._simple_click_pagination(
                    next_button_selector=next_button_selector,
                    max_pages=max_pages,
                    wait_after_click=wait_after_click,
                    extract_articles_config=extract_articles_config
                )

        # 自动检测翻页功能
        if auto_detect_pagination:
            pagination_available = await self._detect_pagination_availability(next_button_selector, timeout)
            if not pagination_available:
                logger.warning("⚠️ 标准模式未检测到有效的翻页功能")
                logger.info("🔄 尝试回退到简单翻页模式...")

                # 智能回退：尝试简单翻页模式
                try:
                    return await self._simple_click_pagination(
                        next_button_selector=next_button_selector,
                        max_pages=max_pages,
                        wait_after_click=wait_after_click,
                        extract_articles_config=extract_articles_config
                    )
                except Exception as e:
                    logger.warning(f"简单翻页模式也失败: {e}")

                    # 最后尝试：仍然提取第一页内容，然后返回
                    if extract_articles_config:
                        try:
                            articles_count = await self.extract_articles_from_page(**extract_articles_config)
                            total_articles_extracted += articles_count
                            logger.info(f"✅ 已提取第1页的 {articles_count} 篇文章")
                        except Exception as e:
                            logger.warning(f"第1页文章提取失败: {e}")

                    logger.info("🔄 所有翻页模式均失败，爬取结束")
                    return current_page

        # 首先提取第一页的文章
        if extract_articles_config:
            try:
                articles_count = await self.extract_articles_from_page(**extract_articles_config)
                total_articles_extracted += articles_count
                logger.info(f"第1页提取到 {articles_count} 篇文章")
                # 新增：记录第一页最后一个item的url
                if stop_on_duplicate_last_item and self.all_articles:
                    last_page_last_item_url = self.all_articles[-1][1] if len(self.all_articles[-1]) > 1 else None
            except Exception as e:
                logger.warning(f"第1页文章提取失败: {e}")

        # 开始翻页循环
        logger.info(f"开始翻页循环，当前页={current_page}, 最大页数={max_pages}")
        if not next_selectors:
            next_selectors = [
                'a:has-text("下一页")',
                'a:has-text("下页")',
                'a:has-text("下一頁")',
                'a.next',
                'a.next-page',
                '.pagination .next',
                'a[title="下一页"]',
                'button:has-text("下一页")',
                next_button_selector  # 保持兼容
            ]

        while current_page < max_pages:
            logger.info(f"尝试翻到第 {current_page + 1} 页...")

            # 等待内容加载
            if content_ready_selector:
                try:
                    await self.page.wait_for_selector(
                        content_ready_selector,
                        state="attached",
                        timeout=timeout
                    )
                    logger.info(f"Page {current_page} content loaded")
                except Exception as e:
                    logger.warning(f"Content loading warning: {str(e)}")

            # 记录翻页前的 URL
            prev_url = self.page.url

            # 每次循环都重新查找 next 按钮，遍历所有 next_selectors
            next_button = None
            next_selector_used = None
            for selector in next_selectors:
                try:
                    btns = await self.page.query_selector_all(selector)
                    for btn in btns:
                        is_visible = await btn.is_visible()
                        if not is_visible:
                            continue
                        is_disabled = await btn.evaluate('(el) => el.hasAttribute("disabled") || el.classList.contains("disabled") || el.classList.contains("unactive") || el.classList.contains("lose")')
                        if is_disabled:
                            logger.info(f"下一页按钮被禁用: {selector}")
                            continue
                        next_button = btn
                        next_selector_used = selector
                        break
                    if next_button:
                        break
                except Exception as e:
                    logger.debug(f"查找下一页按钮 {selector} 出错: {e}")
                    continue

            if not next_button:
                logger.error("❌ 未找到可用的下一页按钮，所有选择器均尝试失败")
                break

            logger.info(f"✅ 找到可用的下一页按钮，选择器: {next_selector_used}")

            # 获取按钮信息用于调试
            button_text = await next_button.text_content()
            button_href = await next_button.get_attribute('href')
            logger.info(f"下一页按钮信息: 文本='{button_text}', href='{button_href}'")


            # 点击下一页按钮
            logger.info("点击下一页按钮...")
            await next_button.click()
            # 等待页面资源加载完毕（参考 test_tjszx_pagination.py）
            try:
                await self.page.wait_for_load_state('networkidle', timeout=timeout)
                logger.info("页面网络空闲，资源加载完毕")
            except Exception as e:
                logger.warning(f"等待 networkidle 超时: {e}")
            # 再额外等待一段时间，确保内容渲染
            await self.page.wait_for_timeout(wait_after_click)
            current_page += 1
            logger.info(f"✅ 成功点击，当前页面: {current_page}")

            # 等待“下一页”按钮消失再出现，或内容区变化（保留，兼容部分站点）
            try:
                await self.page.wait_for_selector(next_selector_used, state="detached", timeout=timeout)
                await self.page.wait_for_selector(next_selector_used, state="attached", timeout=timeout)
                logger.info("下一页按钮已刷新，页面加载完成")
            except Exception as e:
                logger.warning(f"等待下一页按钮刷新时出错: {str(e)}")

            # 翻页后对比 URL 辅助判断
            new_url = self.page.url
            if new_url == prev_url:
                logger.warning("翻页后 URL 未变化，可能翻页失败或为异步加载")

            # 提取当前页的文章
            if extract_articles_config:
                try:
                    logger.info(f"开始提取第{current_page}页的文章...")
                    before_count = len(self.all_articles)
                    articles_count = await self.extract_articles_from_page(**extract_articles_config)
                    total_articles_extracted += articles_count
                    logger.info(f"第{current_page}页提取到 {articles_count} 篇文章")

                    # 新增：判断最后item是否重复
                    if stop_on_duplicate_last_item and self.all_articles:
                        new_last_item_url = self.all_articles[-1][1] if len(self.all_articles[-1]) > 1 else None
                        if last_page_last_item_url and new_last_item_url == last_page_last_item_url:
                            logger.info("检测到最后一项网址与上一页相同，提前结束翻页。")
                            break
                        last_page_last_item_url = new_last_item_url
                except Exception as e:
                    logger.warning(f"第{current_page}页文章提取失败: {e}")

        logger.info(f"翻页完成！总共处理 {current_page} 页，提取 {total_articles_extracted} 篇文章")
        return current_page

    async def _simple_click_pagination(
        self,
        next_button_selector: str,
        max_pages: int,
        wait_after_click: int,
        extract_articles_config: dict = None
    ) -> int:
        """
        简单翻页模式，直接复制test_tjszx_pagination.py的成功逻辑
        """
        logger.info("🔄 开始简单翻页模式（复制成功的测试脚本逻辑）")

        # 使用统一的翻页工具（如果可用）
        if PAGINATION_UTILS_AVAILABLE:
            logger.info("🔧 使用统一翻页工具进行简单翻页")

            # 提取第一页文章
            if extract_articles_config:
                try:
                    articles_count = await self.extract_articles_from_page(**extract_articles_config)
                    logger.info(f"第1页提取到 {articles_count} 篇文章")
                except Exception as e:
                    logger.warning(f"第1页文章提取失败: {e}")

            # 使用统一工具进行翻页
            current_page = 1
            while current_page < max_pages:
                logger.info(f"\n=== 尝试翻页: 第{current_page}页 → 第{current_page + 1}页 ===")

                success = await PaginationUtils.smart_pagination(
                    page=self.page,
                    current_page=current_page,
                    wait_after_click=wait_after_click
                )

                if not success:
                    logger.warning(f"翻页失败，停止在第{current_page}页")
                    break

                current_page += 1

                # 提取当前页文章
                if extract_articles_config:
                    try:
                        articles_count = await self.extract_articles_from_page(**extract_articles_config)
                        logger.info(f"第{current_page}页提取到 {articles_count} 篇文章")
                    except Exception as e:
                        logger.warning(f"第{current_page}页文章提取失败: {e}")

            logger.info(f"简单翻页完成，总共处理 {current_page} 页")
            return current_page

        # 回退到原有逻辑（保持兼容性）
        logger.info("🔧 使用原有简单翻页逻辑")

        next_selectors = [
            'a:has-text("下一页")',
            'a:has-text("下页")',
            'a:has-text("下一頁")',
            '.next',
            'a.next-page',
            '.pagination',
            'a[title="下一页"]',
            'button:has-text("下一页")'
        ]

        # 额外等待时间，确保动态内容加载完成
        logger.info("⏳ 等待动态内容加载完成...")
        await self.page.wait_for_timeout(2000)  # 等待5秒，与原始测试脚本一致

        # 🔧 修复：先检查翻页按钮是否存在，再进行文章提取
        # 这样避免文章提取过程影响翻页按钮的可用性
        logger.info("🔍 预检查翻页按钮可用性...")
        has_pagination = False
        for selector in next_selectors:
            btn = await self.page.query_selector(selector)
            if btn:
                is_disabled = await btn.evaluate('(el) => el.hasAttribute("disabled") || el.classList.contains("disabled")')
                is_visible = await btn.is_visible()
                if not is_disabled and is_visible:
                    has_pagination = True
                    logger.info(f"✅ 预检查发现可用翻页按钮: {selector}")
                    break

        if not has_pagination:
            logger.warning("⚠️ 预检查未发现可用翻页按钮，可能无法翻页")

        # 然后提取第一页的文章
        if extract_articles_config:
            try:
                articles_count = await self.extract_articles_from_page(**extract_articles_config)
                logger.info(f"第1页提取到 {articles_count} 篇文章")
            except Exception as e:
                logger.warning(f"第1页文章提取失败: {e}")

        # 开始翻页循环 - 完全复制test_tjszx_pagination.py的逻辑
        current_page = 1
        click_count = 0
        max_clicks = max_pages - 1  # -1 因为第一页已经处理了

        logger.info(f"📋 翻页参数: max_pages={max_pages}, max_clicks={max_clicks}")

        if max_clicks <= 0:
            logger.warning(f"⚠️ max_clicks={max_clicks} <= 0，无需翻页")
            return current_page

        while click_count < max_clicks:
            logger.info(f"\n=== 尝试翻页: 点击 {click_count+1} ===")

            # 查找当前可用的下一页按钮 - 完全复制原始逻辑，但添加详细调试
            next_btn = None
            logger.info(f"🔍 开始查找下一页按钮，共 {len(next_selectors)} 个选择器")

            for i, selector in enumerate(next_selectors):
                logger.info(f"   [{i+1}] 尝试选择器: {selector}")
                btn = await self.page.query_selector(selector)
                if btn:
                    is_disabled = await btn.evaluate('(el) => el.hasAttribute("disabled") || el.classList.contains("disabled")')
                    is_visible = await btn.is_visible()
                    btn_text = await btn.text_content()
                    btn_href = await btn.get_attribute('href')

                    logger.info(f"       找到元素: 文本='{btn_text}', href='{btn_href}', 可见={is_visible}, 禁用={is_disabled}")

                    if not is_disabled and is_visible:
                        next_btn = btn
                        logger.info(f"✅ 找到可用的下一页按钮: {selector}")
                        break
                    else:
                        logger.info(f"       ❌ 按钮不可用 (可见={is_visible}, 禁用={is_disabled})")
                else:
                    logger.info(f"       ❌ 未找到元素")

            if not next_btn:
                logger.info("❌ 未找到可用的下一页按钮，翻页结束")
                break

            # 记录翻页前的URL
            initial_url = self.page.url
            logger.info(f"当前URL: {initial_url}")

            # 点击下一页按钮 - 完全复制原始逻辑
            await next_btn.click()
            await self.page.wait_for_load_state('networkidle')
            await self.page.wait_for_timeout(2000)

            # 检查URL变化
            new_url = self.page.url
            logger.info(f"翻页后URL: {new_url}")

            if initial_url == new_url:
                logger.info("URL未变化，可能是最后一页或AJAX加载")
            else:
                logger.info("✅ URL已变化 - 使用URL翻页")

            current_page += 1
            click_count += 1

            # 提取当前页的文章
            if extract_articles_config:
                try:
                    logger.info(f"📄 开始提取第{current_page}页的文章...")
                    articles_count = await self.extract_articles_from_page(**extract_articles_config)
                    logger.info(f"✅ 第{current_page}页提取到 {articles_count} 篇文章")
                except Exception as e:
                    logger.warning(f"❌ 第{current_page}页文章提取失败: {e}")

        logger.info(f"🎉 简单翻页完成！总共处理了 {current_page} 页")
        return current_page

    async def _url_based_pagination(
        self,
        max_pages: int,
        wait_after_click: int,
        extract_articles_config: dict = None
    ) -> int:
        """
        基于URL的翻页模式，适用于天津税务局等网站
        根据JavaScript分析，翻页URL格式为：
        http://www.tjszx.gov.cn/tagz/system/count//0009003/000000000000/000/000/c0009003000000000000_000000011.shtml
        """
        logger.info("🔄 开始URL翻页模式")

        # 首先提取第一页的文章
        if extract_articles_config:
            try:
                articles_count = await self.extract_articles_from_page(**extract_articles_config)
                logger.info(f"第1页提取到 {articles_count} 篇文章")
            except Exception as e:
                logger.warning(f"第1页文章提取失败: {e}")

        current_page = 1

        # 根据JavaScript分析构建翻页URL
        # 基础信息
        base_url = "http://www.tjszx.gov.cn/tagz/system/count/"
        channel_id = "0009003000000000000"

        # 开始翻页循环
        for page_num in range(2, max_pages + 1):
            logger.info(f"\n=== 尝试翻页到第 {page_num} 页 ===")

            try:
                # 构建翻页URL（根据JavaScript逻辑）
                # 页码需要倒序计算：maxpage - current_page + 1
                # 假设总页数为12（从JavaScript中获取的maxpage）
                max_page_total = 12
                reverse_page_num = max_page_total - page_num + 1

                # 格式化页码为9位数字
                page_str = f"{reverse_page_num:09d}"

                # 构建完整URL
                next_url = (f"{base_url}/"
                           f"{channel_id[:7]}/"
                           f"{channel_id[7:19]}/"
                           f"{page_str[:3]}/"
                           f"{page_str[3:6]}/"
                           f"c{channel_id}_{page_str}.shtml")

                logger.info(f"🌐 访问第{page_num}页: {next_url}")

                # 访问下一页
                await self.page.goto(next_url, wait_until='networkidle', timeout=30000)
                await self.page.wait_for_timeout(wait_after_click)

                current_page = page_num
                logger.info(f"✅ 成功访问第{page_num}页")

                # 提取当前页的文章
                if extract_articles_config:
                    try:
                        logger.info(f"📄 开始提取第{page_num}页的文章...")
                        articles_count = await self.extract_articles_from_page(**extract_articles_config)
                        logger.info(f"✅ 第{page_num}页提取到 {articles_count} 篇文章")
                    except Exception as e:
                        logger.warning(f"❌ 第{page_num}页文章提取失败: {e}")

            except Exception as e:
                logger.warning(f"❌ 访问第{page_num}页失败: {e}")
                break

        logger.info(f"🎉 URL翻页完成！总共处理了 {current_page} 页")
        return current_page

    async def _detect_pagination_availability(self, next_button_selector, timeout=10000):
        """
        检测页面是否有有效的翻页功能
        :param next_button_selector: 下一页按钮选择器
        :param timeout: 检测超时时间
        :return: True表示有翻页功能，False表示没有
        """
        try:
            logger.info(f"🔍 检测翻页功能，选择器: {next_button_selector}")

            # 1. 检查是否存在下一页按钮
            next_buttons = await self.page.query_selector_all(next_button_selector)
            if not next_buttons:
                logger.info("❌ 未找到下一页按钮")
                return False

            logger.info(f"✅ 找到 {len(next_buttons)} 个匹配的元素")

            # 2. 检查按钮是否可见和可用
            visible_buttons = []
            for i, button in enumerate(next_buttons):
                try:
                    is_visible = await button.is_visible()
                    is_enabled = await button.is_enabled()
                    text = await button.text_content()
                    href = await button.get_attribute('href')
                    onclick = await button.get_attribute('onclick')

                    logger.info(f"  按钮 {i+1}: 可见={is_visible}, 可用={is_enabled}, 文本='{text}', href='{href}', onclick='{onclick}'")

                    if is_visible and is_enabled:
                        # 进一步检查按钮是否有实际功能
                        if href and href != 'javascript:;' and href != '#':
                            visible_buttons.append(button)
                            logger.info(f"    ✅ 按钮 {i+1} 有有效的href链接")
                        elif onclick and onclick.strip():
                            visible_buttons.append(button)
                            logger.info(f"    ✅ 按钮 {i+1} 有onclick事件")
                        elif text and any(keyword in text.lower() for keyword in ['下一页', 'next', '>', '更多']):
                            visible_buttons.append(button)
                            logger.info(f"    ✅ 按钮 {i+1} 有翻页相关文本")
                        else:
                            logger.info(f"    ⚠️ 按钮 {i+1} 可能没有实际功能")
                    else:
                        logger.info(f"    ❌ 按钮 {i+1} 不可见或不可用")

                except Exception as e:
                    logger.warning(f"  检查按钮 {i+1} 时出错: {e}")

            if not visible_buttons:
                logger.info("❌ 没有找到可用的翻页按钮")
                return False

            # 3. 尝试检测页面是否有多页内容的迹象
            pagination_indicators = await self._check_pagination_indicators()
            if not pagination_indicators:
                logger.info("⚠️ 未检测到多页内容的迹象")
                # 但仍然返回True，因为有可用的按钮

            logger.info(f"✅ 检测到有效的翻页功能，找到 {len(visible_buttons)} 个可用按钮")
            return True

        except Exception as e:
            logger.error(f"翻页功能检测失败: {e}")
            return False

    async def _check_pagination_indicators(self) -> bool:
        """
        检查页面是否有多页内容的迹象
        :return: True表示有多页迹象，False表示可能是单页
        """
        try:
            # 检查常见的分页指示器
            pagination_selectors = [
                ".pagination",
                ".page-nav",
                ".page-list",
                ".pager",
                "a[href*='page']",
                "a[href*='index_']",
                ".page-number",
                ".page-item"
            ]

            for selector in pagination_selectors:
                elements = await self.page.query_selector_all(selector)
                if elements:
                    logger.info(f"  ✅ 找到分页指示器: {selector} ({len(elements)} 个)")
                    return True

            # 检查是否有数字链接（页码）
            all_links = await self.page.query_selector_all("a")
            digit_links = 0
            for link in all_links[:20]:  # 只检查前20个链接
                try:
                    text = await link.text_content()
                    if text and text.strip().isdigit():
                        digit_links += 1
                except:
                    continue

            if digit_links >= 2:  # 如果有2个或更多数字链接，可能是页码
                logger.info(f"  ✅ 找到 {digit_links} 个数字链接，可能是页码")
                return True

            logger.info("  ❌ 未找到明显的分页指示器")
            return False

        except Exception as e:
            logger.warning(f"分页指示器检查失败: {e}")
            return False

    async def _find_alternative_pagination_buttons(self) -> list:
        """
        查找替代的翻页按钮
        :return: 可见的翻页按钮列表
        """
        try:
            # 常见的翻页按钮选择器
            alternative_selectors = [
                # 链接类型的翻页按钮
                "a:has-text('下一页')",
                "a:has-text('next')",
                "a:has-text('Next')",
                "a:has-text('>')",
                "a:has-text('>>')",
                "a:has-text('更多')",
                "a[title*='下一页']",
                "a[title*='next']",
                "a[href*='page']",
                "a[href*='index_']",
                "a[onclick*='page']",
                "a[onclick*='Page']",
                ".pagination a:last-child",
                ".page-nav a:last-child",
                ".pager a:last-child",

                # input按钮类型的翻页按钮（常见于政府网站）
                "input[value='下一页']",
                "input[value='next']",
                "input[value='Next']",
                "input[value='>']",
                "input[value='>>']",
                "input[type='button'][value*='下一页']",
                "input[type='submit'][value*='下一页']",
                "input[onclick*='page']",
                "input[onclick*='Page']",
                "input[onclick*='next']",

                # 按钮类型
                "button:has-text('下一页')",
                "button:has-text('next')",
                "button:has-text('Next')",
                "button:has-text('>')",
                "button[onclick*='page']",
                "button[onclick*='next']"
            ]

            found_buttons = []

            for selector in alternative_selectors:
                try:
                    elements = await self.page.query_selector_all(selector)
                    for element in elements:
                        is_visible = await element.is_visible()
                        is_enabled = await element.is_enabled()

                        if is_visible and is_enabled:
                            # 进一步验证这是一个有效的翻页按钮
                            text = await element.text_content()
                            href = await element.get_attribute('href')
                            onclick = await element.get_attribute('onclick')

                            # 检查是否有实际的翻页功能
                            tag_name = await element.evaluate("el => el.tagName.toLowerCase()")

                            # 对于input元素，检查value属性
                            if tag_name == 'input':
                                value = await element.get_attribute('value')
                                input_type = await element.get_attribute('type')
                                if value and any(keyword in value.lower() for keyword in ['下一页', 'next', '>', '更多']):
                                    found_buttons.append(element)
                                    logger.info(f"  ✅ 找到替代按钮: 选择器={selector}, input类型={input_type}, value='{value}'")
                                    continue

                            # 对于其他元素，检查常规属性
                            if (href and href not in ['javascript:;', '#', '']) or \
                               (onclick and onclick.strip()) or \
                               (text and any(keyword in text.lower() for keyword in ['下一页', 'next', '>', '更多'])):
                                found_buttons.append(element)
                                logger.info(f"  ✅ 找到替代按钮: 选择器={selector}, 文本='{text}', href='{href}'")

                except Exception as e:
                    logger.debug(f"检查选择器 {selector} 时出错: {e}")
                    continue

            return found_buttons

        except Exception as e:
            logger.error(f"查找替代翻页按钮失败: {e}")
            return []

    async def _detect_pagination_mode(
        self,
        scroll_container_selector: str = "body",
        load_element_pattern: Optional[str] = None
    ) -> str:
        """
        智能检测网页的翻页模式
        :param scroll_container_selector: 滚动容器选择器
        :param load_element_pattern: 加载元素模式
        :return: "load_elements" 或 "traditional"
        """
        # 1. 如果明确指定了加载元素模式，优先使用
        if load_element_pattern and '{n}' in load_element_pattern:
            logger.info(f"检测到指定的加载元素模式: {load_element_pattern}")
            return "load_elements"

        # 2. 自动检测常见的加载元素模式
        common_load_patterns = [
            "#load{n}",
            "#loadMore{n}",
            ".load{n}",
            ".load-more-{n}",
            "[id^='load'][id$='{n}']"
        ]

        for pattern in common_load_patterns:
            # 检测 #load1, #load2 等元素
            base_pattern = pattern.replace('{n}', '1')
            try:
                element = await self.page.query_selector(base_pattern)
                if element:
                    logger.info(f"自动检测到加载元素模式: {pattern}")
                    return "load_elements"
            except Exception:
                continue

        # 3. 检测当前URL是否包含已知的加载元素网站特征
        current_url = self.page.url
        load_element_sites = [
            "shrd.gov.cn",  # 上海人大网站
            "npc.gov.cn",   # 全国人大网站
            # 可以添加更多已知使用加载元素的网站
        ]

        for site in load_element_sites:
            if site in current_url:
                logger.info(f"检测到已知的加载元素网站: {site}")
                # 为这些网站设置默认的加载元素模式
                return "load_elements"

        # 4. 默认使用传统滚动模式
        logger.info("使用传统滚动模式")
        return "traditional"

    async def scroll_pagination(
        self,
        scroll_container_selector: str = "body",
        scroll_step: int = 1000,
        scroll_delay: int = 1000,
        max_scrolls: int = 20,
        load_indicator_selector: Optional[str] = None,
        scroll_timeout: int = 10000,
        height_tolerance: int = 50,
        load_element_pattern: Optional[str] = None,
        max_loads: int = 50,
        extract_articles_config: dict = None
    ) -> int:
        """
        【处理器标识】: HANDLER_SCROLL_PAGINATION
        【适用场景】: 无限滚动网站、社交媒体、新闻聚合网站等使用滚动加载更多内容的现代Web应用
        【处理条件】:
            - 页面支持滚动加载机制
            - 滚动到底部时会触发新内容加载
            - 页面高度会随内容增加而变化，或有动态加载元素
        【处理流程】:
            1. 智能检测翻页模式（传统滚动 vs 加载元素模式）
            2. 根据检测结果选择相应的处理策略：
               - 传统滚动模式：基于页面高度变化检测
               - 加载元素模式：基于#load{n}等动态元素检测
            3. 循环执行滚动操作直到无新内容或达到限制
            4. 每次滚动后等待内容加载并提取文章信息
        【依赖模块】:
            - Playwright页面滚动API
            - _detect_pagination_mode() (翻页模式检测)
            - _traditional_scroll_pagination() (传统滚动处理)
            - _load_element_scroll_pagination() (加载元素处理)
        【成功标准】: 成功触发滚动加载并检测到新内容出现
        【失败处理】:
            - 滚动无效果时自动停止
            - 加载超时时记录警告并继续
            - 达到最大滚动次数时正常结束
        【性能考虑】:
            - 合理设置scroll_delay避免过快滚动
            - 使用height_tolerance减少无效检测
            - 设置合理的超时时间避免无限等待
        【维护说明】:
            - 不同网站的滚动触发机制可能不同，需要调整参数
            - 注意处理懒加载图片和异步内容的加载时间
            - 定期检查加载元素模式的选择器模式
        【测试用例】:
            - 社交媒体网站: 微博、Twitter等无限滚动
            - 新闻聚合网站: 今日头条、知乎等
            - 预期结果: 成功滚动加载多屏内容

        Args:
            scroll_container_selector: 滚动容器的CSS选择器
            scroll_step: 每次滚动像素数
            scroll_delay: 滚动间隔(毫秒)
            max_scrolls: 最大滚动次数（传统模式）
            load_indicator_selector: 加载指示器的选择器（传统模式）
            scroll_timeout: 滚动加载超时时间
            height_tolerance: 高度变化容忍值（传统模式）
            load_element_pattern: 加载元素模式，如 "#load{n}"（加载元素模式）
            max_loads: 最大加载次数（加载元素模式）
            extract_articles_config: 文章提取配置（加载元素模式）

        Returns:
            int: 实际滚动/加载次数
        """
        logger.info("开始智能滚动翻页...")

        # 智能检测翻页模式
        detected_mode = await self._detect_pagination_mode(
            scroll_container_selector=scroll_container_selector,
            load_element_pattern=load_element_pattern
        )

        # 根据检测结果选择模式
        if detected_mode == "load_elements":
            # 如果没有指定load_element_pattern，使用默认的#load{n}
            if not load_element_pattern or '{n}' not in load_element_pattern:
                load_element_pattern = "#load{n}"
                logger.info(f"使用默认加载元素模式: {load_element_pattern}")

            return await self._scroll_with_load_elements(
                load_element_pattern=load_element_pattern,
                max_loads=max_loads,
                scroll_delay=scroll_delay,
                scroll_step=scroll_step,
                extract_articles_config=extract_articles_config
            )
        else:
            # 使用传统滚动模式
            return await self._scroll_traditional(
                scroll_container_selector=scroll_container_selector,
                scroll_step=scroll_step,
                scroll_delay=scroll_delay,
                max_scrolls=max_scrolls,
                load_indicator_selector=load_indicator_selector,
                scroll_timeout=scroll_timeout,
                height_tolerance=height_tolerance
            )

    async def _scroll_traditional(
        self,
        scroll_container_selector: str,
        scroll_step: int,
        scroll_delay: int,
        max_scrolls: int,
        load_indicator_selector: Optional[str],
        scroll_timeout: int,
        height_tolerance: int
    ) -> int:
        """
        传统滚动模式：基于高度变化检测
        """
        logger.info(f"使用传统滚动模式，容器: {scroll_container_selector}")
        scroll_count = 0
        last_height = 0

        # 获取滚动容器
        try:
            container = await self.page.wait_for_selector(
                scroll_container_selector,
                state="attached",
                timeout=scroll_timeout
            )

            is_hidden = await container.is_hidden()
            if is_hidden:
                logger.warning(f"滚动容器 {scroll_container_selector} 被隐藏，尝试显示")
                await container.scroll_into_view_if_needed()
                await self.page.wait_for_timeout(1000)

        except Exception as e:
            logger.error(f"滚动容器未找到: {str(e)}")

            # 尝试备用选择器
            backup_selectors = ["body", "html", ".content", "#content", ".main"]
            for backup_selector in backup_selectors:
                try:
                    logger.info(f"尝试备用选择器: {backup_selector}")
                    container = await self.page.wait_for_selector(backup_selector, timeout=2000)
                    logger.info(f"成功找到备用容器: {backup_selector}")
                    break
                except:
                    continue
            else:
                logger.error("未找到合适的滚动容器")
                return 0

        while scroll_count < max_scrolls:
            # 执行滚动
            await container.evaluate(f"container => container.scrollBy(0, {scroll_step})")
            await self.page.wait_for_timeout(scroll_delay)

            # 获取当前滚动高度
            new_height = await container.evaluate("container => container.scrollHeight")

            # 检测是否滚动到底部
            if abs(new_height - last_height) <= height_tolerance:
                logger.info("没有更多内容可加载（滚动高度未变化）")
                break

            last_height = new_height
            scroll_count += 1
            logger.info(f"已滚动 {scroll_count} 次，高度: {new_height}px")

            # 等待加载完成
            if load_indicator_selector:
                # 检查是否是无效的 {n} 模式选择器
                if '{n}' in load_indicator_selector:
                    logger.warning(f"检测到无效的加载指示器选择器: {load_indicator_selector}")
                    logger.warning("这应该使用加载元素模式，而不是传统滚动模式")
                else:
                    try:
                        await self.page.wait_for_selector(
                            load_indicator_selector,
                            state="visible",
                            timeout=2000
                        )
                        await self.page.wait_for_selector(
                            load_indicator_selector,
                            state="hidden",
                            timeout=10000
                        )
                        logger.info("加载指示器处理完成")
                    except Exception as e:
                        logger.warning(f"加载指示器错误: {str(e)}")

        return scroll_count

    async def _scroll_with_load_elements(
        self,
        load_element_pattern: str,
        max_loads: int,
        scroll_delay: int,
        scroll_step: int,
        extract_articles_config: dict = None
    ) -> int:
        """
        加载元素模式：基于 #load{n} 等动态元素检测
        """
        logger.info(f"使用加载元素模式: {load_element_pattern}")
        logger.info(f"最大加载次数: {max_loads}, 滚动延迟: {scroll_delay}ms")

        load_count = 0
        total_articles_extracted = 0

        # 首先提取第一页的文章
        if extract_articles_config:
            try:
                articles_count = await self.extract_articles_from_page(**extract_articles_config)
                total_articles_extracted += articles_count
                logger.info(f"初始页面提取到 {articles_count} 篇文章")
            except Exception as e:
                logger.warning(f"初始页面文章提取失败: {e}")

        # 开始滚动加载循环
        consecutive_failures = 0
        max_consecutive_failures = 1

        # 添加重复检测
        last_urls_count = 0
        consecutive_no_new_urls = 0
        max_consecutive_no_new = 3  # 连续3次没有新URL则停止

        for i in range(1, max_loads + 1):
            try:
                # 构造当前加载元素的选择器
                current_load_selector = load_element_pattern.replace("{n}", str(i))
                logger.info(f"第{i}次加载，查找元素: {current_load_selector}")

                # 持续滚动直到找到加载元素或超时
                element_found = False
                scroll_attempts = 0
                max_scroll_attempts = 10

                while not element_found and scroll_attempts < max_scroll_attempts:
                    logger.info(f"第{scroll_attempts + 1}次滚动，寻找元素: {current_load_selector}")
                    await self.page.evaluate(f"window.scrollBy(0, {scroll_step})")
                    await self.page.wait_for_timeout(scroll_delay)

                    # 检查加载元素是否出现
                    try:
                        load_element = await self.page.wait_for_selector(
                            current_load_selector,
                            state="attached",
                            timeout=3000
                        )

                        if load_element:
                            logger.info(f"✅ 找到加载元素 {current_load_selector}")
                            element_found = True
                            load_count += 1
                            consecutive_failures = 0

                            # 等待加载元素完全加载完成
                            logger.info("等待加载元素完成加载...")
                            await self.page.wait_for_timeout(2000)

                            # 提取新加载的文章
                            if extract_articles_config:
                                try:
                                    before_count = len(self.all_articles)
                                    articles_count = await self.extract_articles_from_page(**extract_articles_config)
                                    after_count = len(self.all_articles)
                                    new_articles = after_count - before_count
                                    total_articles_extracted += new_articles
                                    logger.info(f"第{i}次加载提取到 {new_articles} 篇新文章")
                                except Exception as e:
                                    logger.warning(f"第{i}次加载文章提取失败: {e}")

                            # 继续滚动以触发下一次加载
                            logger.info("继续滚动以触发下一次加载...")
                            await self.page.evaluate(f"window.scrollBy(0, {scroll_step})")
                            await self.page.wait_for_timeout(1000)

                        else:
                            logger.info(f"❌ 未找到加载元素 {current_load_selector}")
                            break

                    except Exception as e:
                        logger.debug(f"第{scroll_attempts + 1}次滚动未找到元素: {current_load_selector}")
                        scroll_attempts += 1

                        if scroll_attempts >= max_scroll_attempts:
                            logger.info(f"❌ 经过{max_scroll_attempts}次滚动仍未找到 {current_load_selector}")
                            logger.info("可能已到达最后一页或加载完成")
                            consecutive_failures += 1
                            break

                # 如果没有找到元素，增加连续失败计数
                if not element_found:
                    consecutive_failures += 1
                    logger.info(f"连续失败次数: {consecutive_failures}/{max_consecutive_failures}")

                    if consecutive_failures >= max_consecutive_failures:
                        logger.info("连续失败次数过多，停止加载")
                        break

                # 检查是否有新URL
                current_urls_count = len(set([article[1] for article in self.all_articles if len(article) > 1]))
                if current_urls_count == last_urls_count:
                    consecutive_no_new_urls += 1
                    logger.info(f"连续 {consecutive_no_new_urls} 次没有新URL")
                    if consecutive_no_new_urls >= max_consecutive_no_new:
                        logger.info("连续多次没有新URL，停止滚动")
                        break
                else:
                    consecutive_no_new_urls = 0
                
                last_urls_count = current_urls_count

            except Exception as e:
                logger.error(f"第{i}次滚动加载出错: {str(e)}")
                consecutive_failures += 1
                if consecutive_failures >= max_consecutive_failures:
                    break

        logger.info(f"加载元素模式完成！总共加载 {load_count} 次，提取 {total_articles_extracted} 篇文章")
        return load_count



    async def handle_iframe_pagination(
        self,
        iframe_selector: str,
        pagination_type: str,
        **kwargs
    ) -> int:
        """
        处理iframe内的翻页
        :param iframe_selector: iframe的CSS选择器
        :param pagination_type: 翻页类型 ('click' 或 'scroll')
        :param kwargs: 传递给翻页方法的参数
        :return: 实际翻页/滚动次数
        """
        try:
            # 定位iframe
            frame_element = await self.page.wait_for_selector(iframe_selector)
            frame = await frame_element.content_frame()
            
            if not frame:
                raise ValueError(f"Could not get frame from selector: {iframe_selector}")
            
            # 创建iframe内的页面处理器
            iframe_handler = PaginationHandler(frame)
            
            if pagination_type == 'click':
                return await iframe_handler.click_pagination(**kwargs)
            elif pagination_type == 'scroll':
                return await iframe_handler.scroll_pagination(**kwargs)
            else:
                raise ValueError("Unsupported pagination type. Use 'click' or 'scroll'")
        except Exception as e:
            logger.error(f"Iframe pagination error: {str(e)}")
            return 0

    def add_articles(self, articles_list):
        """
        添加文章到all_articles列表

        Args:
            articles_list: 文章列表，格式为 [(title, href, save_dir, page_title, page_url, classid), ...]
        """
        if articles_list:
            self.all_articles.extend(articles_list)
            logger.info(f"添加了 {len(articles_list)} 篇文章，总计 {len(self.all_articles)} 篇")

    def get_all_articles(self):
        """
        获取所有收集到的文章

        Returns:
            list: 所有文章的列表
        """
        return self.all_articles.copy()

    def clear_articles(self):
        """清空文章列表"""
        self.all_articles.clear()
        logger.info("已清空文章列表")

    def get_articles_count(self):
        """获取文章总数"""
        return len(self.all_articles)

    async def extract_articles_from_page(self,
                                       list_container_selector=".main",
                                       article_item_selector=".clearfix.ty_list li a",
                                       title_selector=None,
                                       list_container_type="CSS",
                                       article_item_type="CSS",
                                       title_selector_type="CSS",
                                       save_dir="articles",
                                       page_title="动态翻页结果",
                                       classid="",
                                       base_url=None,
                                       url_mode="relative"):
        """
        从当前页面提取文章信息并添加到all_articles
        参考core.crawler.py的get_article_links和get_full_link函数逻辑

        Args:
            list_container_selector: 列表容器选择器
            article_item_selector: 文章项选择器
            title_selector: 标题选择器（可选）
            list_container_type: 列表容器选择器类型 (CSS/XPath)
            article_item_type: 文章项选择器类型 (CSS/XPath)
            title_selector_type: 标题选择器类型 (CSS/XPath)
            save_dir: 保存目录
            page_title: 页面标题
            classid: 分类ID
            base_url: 基础URL，用于构建完整链接
            url_mode: URL模式 ('absolute'/'relative')

        Returns:
            int: 本页提取到的文章数量
        """
        try:
            # 等待页面加载
            await self.page.wait_for_load_state('networkidle', timeout=10000)

            current_url = self.page.url
            if not base_url:
                base_url = current_url

            # 查找列表容器 - 支持多个容器（参考core.crawler.py的get_article_links）
            if list_container_type.upper() == "XPATH":
                containers = await self.page.query_selector_all(f"xpath={list_container_selector}")
            else:
                containers = await self.page.query_selector_all(list_container_selector)

            if not containers:
                logger.warning(f"未找到列表容器: {list_container_selector}")
                return 0

            current_page_articles = []
            total_articles_found = 0

            # 遍历所有容器（参考core.crawler.py逻辑）
            for container in containers:
                try:
                    # 查找文章项
                    if article_item_type.upper() == "XPATH":
                        articles = await container.query_selector_all(f"xpath={article_item_selector}")
                    else:
                        articles = await container.query_selector_all(article_item_selector)

                    if not articles:
                        continue

                    total_articles_found += len(articles)

                    # 处理每个文章项（参考core.crawler.py的get_article_links逻辑）
                    for article in articles:
                        try:
                            # 增强的链接提取逻辑（参考core.crawler.py）
                            href = await article.get_attribute('href')

                            # 若没有href，尝试查找子元素的a标签
                            if not href:
                                try:
                                    a_tag = await article.query_selector('a')
                                    if a_tag:
                                        href = await a_tag.get_attribute('href')
                                except Exception:
                                    pass

                            # 如果还是没有，尝试从onclick提取（参考core.crawler.py逻辑）
                            if not href:
                                onclick = await article.get_attribute('onclick')
                                if onclick:
                                    # 使用正则表达式提取URL（与core.crawler.py相同的模式）
                                    import re
                                    patterns = [
                                        r"location(?:\.href)?\s*=\s*['\"]([^'\"]+)",  # location='url'
                                        r"window\.open\s*\(\s*['\"]([^'\"]+)",       # window.open('url')
                                        r"window\.location\.href\s*=\s*['\"]([^'\"]+)",  # window.location.href='url'
                                        r"redirectTo\s*\(\s*['\"]([^'\"]+)"           # 自定义函数如 redirectTo('url')
                                    ]

                                    for pattern in patterns:
                                        match = re.search(pattern, onclick)
                                        if match:
                                            href = match.group(1)
                                            break

                            # 增强的标题提取逻辑（参考core.crawler.py）
                            title = ""
                            if title_selector:
                                try:
                                    if title_selector_type.upper() == "XPATH":
                                        title_element = await article.query_selector(f"xpath={title_selector}")
                                    else:
                                        title_element = await article.query_selector(title_selector)
                                    if title_element:
                                        title = await title_element.text_content()
                                except Exception:
                                    pass

                            # 如果没有专门的标题选择器或提取失败，使用文章元素的文本
                            if not title:
                                title = await article.text_content()

                            if title:
                                title = title.strip()

                            # 处理完整URL（参考core.crawler.py的get_full_link逻辑）
                            if href:
                                full_url = self._get_full_link(href, current_url, base_url, url_mode)

                                # 构建文章信息元组，格式与core.crawler.py兼容
                                article_info = (title, full_url, save_dir, page_title, current_url, classid)
                                current_page_articles.append(article_info)

                        except Exception as e:
                            logger.warning(f"提取单个文章信息时出错: {e}")
                            continue

                except Exception as e:
                    logger.warning(f"处理容器时出错: {e}")
                    continue

            # 添加到总列表
            if current_page_articles:
                self.add_articles(current_page_articles)
                logger.info(f"从当前页面提取到 {len(current_page_articles)} 篇文章")

            # 添加URL去重逻辑
            unique_articles = []
            seen_urls = set()
            
            for article in self.all_articles:
                url = article[1] if len(article) > 1 else None
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_articles.append(article)
            
            # 更新文章列表为去重后的列表
            self.all_articles = unique_articles
            
            return len(current_page_articles)

        except Exception as e:
            logger.error(f"从页面提取文章时出错: {e}")
            return 0

    def _get_full_link(self, href, input_url, base_url, url_mode):
        """
        获取完整的链接（参考core.crawler.py的get_full_link函数）

        Args:
            href: 原始链接
            input_url: 输入URL
            base_url: 基础URL
            url_mode: URL模式 ('absolute'/'relative')

        Returns:
            str: 完整的URL
        """
        if not href:
            return ''

        # 绝对URL
        if href.startswith(('http://', 'https://')):
            return href

        # 协议相对URL
        if href.startswith('//'):
            scheme = base_url.split(':')[0] if ':' in base_url else 'https'
            return f"{scheme}:{href}"

        # 锚点
        if href.startswith('#'):
            from urllib.parse import urljoin
            return urljoin(base_url, href)

        # 相对路径处理（与core.crawler.py逻辑一致）
        from urllib.parse import urljoin
        if url_mode == "absolute":
            return urljoin(input_url, href)
        elif url_mode == "relative":
            return urljoin(base_url, href)
        else:
            return urljoin(base_url, href)

# ==================== 浏览器启动函数 ====================

async def launch_browser(
    p,  # 新增参数
    headless: bool = False,
    browser_type: str = "chromium",
    slow_mo: int = 100,
    proxy: Optional[Dict] = None,
    viewport: Dict[str, int] = {"width": 1280, "height": 720},
    user_agent: Optional[str] = None,
    **launch_kwargs
) -> tuple[Browser, BrowserContext, Page]:
    """
    启动Playwright浏览器
    :param p: async_playwright 实例
    :param headless: 是否无头模式
    :param browser_type: 浏览器类型 (chromium, firefox, webkit)
    :param slow_mo: 操作延迟(毫秒)
    :param proxy: 代理设置 {'server': 'http://host:port'}
    :param viewport: 视口大小 {'width': 1280, 'height': 720}
    :param user_agent: 自定义User-Agent
    :return: (browser, context, page)
    """
    # 选择浏览器类型
    browser_launcher = getattr(p, browser_type).launch
    
    # 准备启动参数
    launch_options = {
        "headless": headless,
        "slow_mo": slow_mo,
        **launch_kwargs
    }
    
    # 添加代理设置
    if proxy:
        launch_options["proxy"] = proxy
    
    browser = await browser_launcher(**launch_options)
    
    # 创建上下文
    context_options = {"viewport": viewport}
    if user_agent:
        context_options["user_agent"] = user_agent
    
    context = await browser.new_context(**context_options)
    page = await context.new_page()
    
    return browser, context, page






