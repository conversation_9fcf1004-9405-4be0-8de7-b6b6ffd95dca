#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两个问题：
1. 自动命名模式保存文件问题
2. P标签内容判空问题
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_p_tag_content():
    """测试P标签内容提取"""
    print("🧪 测试P标签内容提取...")
    print("=" * 50)
    
    try:
        from playwright.async_api import async_playwright
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False)
            page = await browser.new_page()
            
            # 访问测试URL
            test_url = "http://www.hfszx.org.cn/hfzx/web/article.jsp?strId=1555653138650071&strWebSiteId=1354153871125000&strColId=1508480969277019"
            print(f"📄 访问URL: {test_url}")
            
            await page.goto(test_url, wait_until='networkidle')
            
            # 测试P标签选择器
            p_elements = await page.query_selector_all('p')
            print(f"📝 找到 {len(p_elements)} 个P标签")
            
            all_p_content = []
            for i, elem in enumerate(p_elements):
                try:
                    text = await elem.text_content()
                    if text and text.strip():
                        all_p_content.append(text.strip())
                        print(f"  P{i+1}: {text.strip()[:100]}...")
                except:
                    continue
            
            print(f"\n📊 有效P标签内容数量: {len(all_p_content)}")
            
            # 合并所有P标签内容
            combined_content = "\n".join(all_p_content)
            print(f"📏 合并后内容长度: {len(combined_content)} 字符")
            
            # 测试是否为空的判断逻辑
            from core.crawler import is_content_empty, is_all_selectors_content_empty
            
            is_empty_single = is_content_empty(combined_content)
            is_empty_all = is_all_selectors_content_empty(all_p_content)
            
            print(f"🔍 单个内容判空结果: {is_empty_single}")
            print(f"🔍 所有选择器判空结果: {is_empty_all}")
            
            # 如果判断为空，分析原因
            if is_empty_all:
                print("\n❌ 内容被判断为空，分析原因:")
                import re
                text_only = re.sub(r'<[^>]+>', '', combined_content)
                text_only = re.sub(r'[\s\n\r\t\u00a0\u3000]+', '', text_only)
                print(f"   清理后文本长度: {len(text_only)}")
                print(f"   清理后文本预览: {text_only[:200]}...")
            else:
                print("✅ 内容判断正常")
            
            await browser.close()
            
    except Exception as e:
        print(f"❌ 测试P标签内容失败: {e}")

def test_auto_naming():
    """测试自动命名功能"""
    print("\n🧪 测试自动命名功能...")
    print("=" * 50)
    
    try:
        from gui.main_window import CrawlerGUI
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = CrawlerGUI()
        
        # 模拟设置分类和配置
        print("📋 模拟设置分类信息...")
        
        # 设置测试数据
        gui.parent_category_combo.addItem("政府机构")
        gui.parent_category_combo.setCurrentText("政府机构")
        
        gui.sub_category_combo.addItem("政协系统")
        gui.sub_category_combo.setCurrentText("政协系统")
        
        gui.child_category_combo.addItem("合肥政协")
        gui.child_category_combo.setCurrentText("合肥政协")
        
        gui.config_combo.addItem("参言建议")
        gui.config_combo.setCurrentText("参言建议")
        
        # 设置自动命名模式
        gui.filename_mode_combo.setCurrentText("自动命名")
        
        # 测试文件名生成
        filename = gui.generate_filename_from_category_path()
        print(f"📝 生成的文件名: {filename}")
        
        # 测试有效文件名获取
        effective_filename = gui.get_effective_export_filename()
        print(f"📝 有效文件名: {effective_filename}")
        
        # 测试配置获取
        config_data = gui.get_config_from_gui()
        export_filename = config_data.get('export_filename', '')
        print(f"📝 配置中的导出文件名: {export_filename}")
        
        if filename == "合肥政协_参言建议":
            print("✅ 自动命名功能正常")
        else:
            print(f"❌ 自动命名功能异常，期望: 合肥政协_参言建议，实际: {filename}")
            
    except Exception as e:
        print(f"❌ 测试自动命名功能失败: {e}")

async def main():
    """主测试函数"""
    print("🔧 开始问题诊断测试...")
    print("=" * 60)
    
    # 测试1：P标签内容提取
    await test_p_tag_content()
    
    # 测试2：自动命名功能
    test_auto_naming()
    
    print("\n" + "=" * 60)
    print("🏁 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
