#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际爬虫测试 - 测试合肥政协网站的P标签内容提取
"""

import sys
import os
import asyncio
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_real_crawling():
    """测试实际的爬虫功能"""
    print("🧪 测试实际爬虫功能...")
    print("=" * 60)
    
    try:
        from core.crawler import save_article_async
        
        # 测试URL
        test_url = "http://www.hfszx.org.cn/hfzx/web/article.jsp?strId=1555653138650071&strWebSiteId=1354153871125000&strColId=1508480969277019"
        
        # 测试参数
        save_dir = "articles"
        page_title = "测试页面"
        content_selectors = ["p"]  # 使用P标签选择器
        export_filename = "合肥政协_参言建议"
        file_format = "Excel"
        
        print(f"📄 测试URL: {test_url}")
        print(f"📁 保存目录: {save_dir}")
        print(f"📝 导出文件名: {export_filename}")
        print(f"🔍 内容选择器: {content_selectors}")
        
        # 确保目录存在
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        # 调用爬虫函数
        print("\n🚀 开始爬取...")
        result = await save_article_async(
            link=test_url,
            save_dir=save_dir,
            page_title=page_title,
            content_selectors=content_selectors,
            title_selectors=["h1", ".title"],
            date_selectors=[".date", "time"],
            source_selectors=[".source"],
            export_filename=export_filename,
            file_format=file_format,
            mode="safe",  # 使用安全模式（Playwright）
            retry=1,
            interval=0
        )
        
        print(f"\n📊 爬取结果: {result}")
        
        # 检查文件是否生成
        expected_file = os.path.join(save_dir, f"{export_filename}.xlsx")
        if os.path.exists(expected_file):
            print(f"✅ 文件已生成: {expected_file}")
            
            # 检查文件大小
            file_size = os.path.getsize(expected_file)
            print(f"📏 文件大小: {file_size} 字节")
            
            if file_size > 0:
                print("✅ 文件不为空，爬取成功")
                return True
            else:
                print("❌ 文件为空，可能爬取失败")
                return False
        else:
            print(f"❌ 文件未生成: {expected_file}")
            return False
            
    except Exception as e:
        print(f"❌ 爬取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_content_extraction():
    """测试内容提取的详细过程"""
    print("\n🧪 测试内容提取详细过程...")
    print("=" * 60)
    
    try:
        from playwright.async_api import async_playwright
        from core.crawler import is_all_selectors_content_empty
        
        test_url = "http://www.hfszx.org.cn/hfzx/web/article.jsp?strId=1555653138650071&strWebSiteId=1354153871125000&strColId=1508480969277019"
        
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            page = await browser.new_page()
            
            print(f"📄 访问URL: {test_url}")
            await page.goto(test_url, wait_until='networkidle')
            
            # 测试P标签提取
            print("\n🔍 测试P标签内容提取...")
            p_elements = await page.query_selector_all('p')
            print(f"📝 找到 {len(p_elements)} 个P标签")
            
            all_content_results = []
            for i, elem in enumerate(p_elements):
                try:
                    text = await elem.text_content()
                    if text and text.strip():
                        all_content_results.append(text.strip())
                        print(f"  P{i+1}: {text.strip()[:100]}...")
                except:
                    continue
            
            print(f"\n📊 有效内容数量: {len(all_content_results)}")
            
            # 测试判空逻辑
            is_empty = is_all_selectors_content_empty(all_content_results)
            print(f"🔍 内容判空结果: {is_empty}")
            
            if not is_empty:
                print("✅ 内容提取正常，不为空")
                
                # 显示合并后的内容
                combined_content = "\n".join(all_content_results)
                print(f"📏 合并后内容长度: {len(combined_content)} 字符")
                print(f"📝 内容预览: {combined_content[:200]}...")
                
                await browser.close()
                return True
            else:
                print("❌ 内容被判断为空")
                await browser.close()
                return False
                
    except Exception as e:
        print(f"❌ 内容提取测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔧 开始实际爬虫测试...")
    print("=" * 80)
    
    # 测试1：内容提取
    content_ok = await test_content_extraction()
    
    # 测试2：实际爬取
    crawl_ok = await test_real_crawling()
    
    print("\n" + "=" * 80)
    print("🏁 实际测试完成")
    print(f"   内容提取: {'✅ 正常' if content_ok else '❌ 异常'}")
    print(f"   实际爬取: {'✅ 正常' if crawl_ok else '❌ 异常'}")
    
    if content_ok and crawl_ok:
        print("\n🎉 实际爬虫测试通过！")
    else:
        print("\n⚠️ 实际爬虫测试存在问题")

if __name__ == "__main__":
    asyncio.run(main())
