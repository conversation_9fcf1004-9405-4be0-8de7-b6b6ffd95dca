#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel写入器类
整合所有Excel相关功能，提供统一的接口
"""

import os
import csv
import time
import threading
import asyncio
import logging
from enum import Enum
from typing import List, Optional, Dict, Any

logger = logging.getLogger('ExcelWriter')

# Excel支持检查
try:
    import openpyxl
    from openpyxl import Workbook
    EXCEL_AVAILABLE = True
except ImportError:
    EXCEL_AVAILABLE = False
    logger.warning("openpyxl库未安装，Excel功能不可用。请运行: pip install openpyxl")


class ExcelWriteMode(Enum):
    """Excel写入模式枚举"""
    DIRECT = "direct"      # 直接写入（传统模式）
    BATCH = "batch"        # 批量写入
    SMART = "smart"        # 智能批量写入
    HYBRID = "hybrid"      # 混合策略（CSV转Excel）


class ExcelWriter:
    """
    Excel写入器类
    提供多种Excel写入策略和统一的接口
    """
    
    def __init__(self, max_workers=3, default_batch_size=50):
        """
        初始化Excel写入器
        
        Args:
            max_workers: 最大并发写入线程数
            default_batch_size: 默认批量大小
        """
        self.max_workers = max_workers
        self.default_batch_size = default_batch_size
        
        # 线程安全相关
        self._write_semaphore = threading.Semaphore(max_workers)
        self._file_locks = {}
        self._locks_lock = threading.Lock()
        
        # 异步相关
        self._async_file_locks = {}
        self._async_locks_lock = None
        self._thread_pool = None
        
        # 批量写入缓存
        self._batch_cache = {}  # 文件路径 -> {'data': [], 'headers': None, 'lock': Lock}
        self._batch_lock = threading.Lock()
        
        logger.info(f"ExcelWriter初始化完成: max_workers={max_workers}, batch_size={default_batch_size}")
    
    def _get_file_lock(self, file_path: str) -> threading.Lock:
        """获取文件级别的锁"""
        with self._locks_lock:
            if file_path not in self._file_locks:
                self._file_locks[file_path] = threading.Lock()
            return self._file_locks[file_path]
    
    async def _get_async_file_lock(self, file_path: str) -> asyncio.Lock:
        """获取文件级别的异步锁"""
        if self._async_locks_lock is None:
            self._async_locks_lock = asyncio.Lock()
        
        async with self._async_locks_lock:
            if file_path not in self._async_file_locks:
                self._async_file_locks[file_path] = asyncio.Lock()
            return self._async_file_locks[file_path]
    
    def _get_thread_pool(self):
        """获取线程池"""
        if self._thread_pool is None:
            import concurrent.futures
            self._thread_pool = concurrent.futures.ThreadPoolExecutor(
                max_workers=self.max_workers,
                thread_name_prefix="ExcelWriter"
            )
        return self._thread_pool
    
    def _get_batch_cache(self, file_path: str) -> Dict[str, Any]:
        """获取文件的批量缓存"""
        with self._batch_lock:
            if file_path not in self._batch_cache:
                self._batch_cache[file_path] = {
                    'data': [],
                    'headers': None,
                    'lock': threading.Lock()
                }
            return self._batch_cache[file_path]
    
    def write_direct(self, file_path: str, data_row: List, headers: Optional[List] = None, 
                    max_retries: int = 3) -> bool:
        """
        直接写入模式（传统模式）
        每次调用都直接写入文件
        
        Args:
            file_path: Excel文件路径
            data_row: 数据行
            headers: 表头
            max_retries: 最大重试次数
        
        Returns:
            bool: 写入是否成功
        """
        if not EXCEL_AVAILABLE:
            logger.error("Excel功能不可用，请安装openpyxl库")
            return False
        
        with self._write_semaphore:
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path and not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    logger.info(f"创建目录: {dir_path}")
                except Exception as e:
                    logger.error(f"创建目录失败: {dir_path}, 错误: {e}")
                    return False

            file_lock = self._get_file_lock(file_path)

            with file_lock:
                for attempt in range(max_retries):
                    try:
                        # 检查文件是否存在
                        file_exists = os.path.exists(file_path)
                        write_headers = False
                        
                        if file_exists:
                            try:
                                wb = openpyxl.load_workbook(file_path)
                                ws = wb.active
                                
                                # 检查是否需要写入表头
                                if ws.max_row <= 1 and headers:
                                    write_headers = True
                            except Exception as e:
                                logger.warning(f"Excel文件可能损坏，将重新创建: {e}")
                                # 备份损坏文件
                                timestamp = int(time.time())
                                backup_path = f"{file_path}.backup_{timestamp}"
                                try:
                                    if os.path.exists(file_path):
                                        os.rename(file_path, backup_path)
                                        logger.info(f"已备份损坏文件到: {backup_path}")
                                except Exception as backup_e:
                                    logger.warning(f"备份文件失败: {backup_e}")
                                
                                wb = Workbook()
                                ws = wb.active
                                write_headers = True if headers else False
                        else:
                            wb = Workbook()
                            ws = wb.active
                            write_headers = True if headers else False
                        
                        # 写入表头
                        if write_headers and headers:
                            ws.append(headers)
                        
                        # 写入数据行
                        ws.append(data_row)
                        
                        # 保存文件
                        try:
                            wb.save(file_path)
                        except PermissionError:
                            # 如果文件被占用，使用临时文件
                            import uuid
                            temp_path = f"{file_path}.tmp_{uuid.uuid4().hex[:8]}"
                            wb.save(temp_path)
                            os.replace(temp_path, file_path)
                        
                        logger.debug(f"Excel直接写入成功: {file_path}")
                        return True
                    
                    except PermissionError as e:
                        logger.warning(f"Excel文件被占用，重试 {attempt + 1}/{max_retries}: {e}")
                        if attempt < max_retries - 1:
                            time.sleep(0.5 * (attempt + 1))
                        continue
                    
                    except Exception as e:
                        logger.error(f"Excel写入失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                        if attempt < max_retries - 1:
                            time.sleep(0.2)
                        continue
                
                logger.error(f"Excel写入失败，已重试 {max_retries} 次: {file_path}")
                return False
    
    def write_batch(self, file_path: str, data_rows: List[List], headers: Optional[List] = None,
                   max_retries: int = 3) -> bool:
        """
        批量写入模式
        一次性写入多行数据
        
        Args:
            file_path: Excel文件路径
            data_rows: 数据行列表
            headers: 表头
            max_retries: 最大重试次数
        
        Returns:
            bool: 写入是否成功
        """
        if not EXCEL_AVAILABLE:
            logger.error("Excel功能不可用，请安装openpyxl库")
            return False
        
        if not data_rows:
            return True
        
        with self._write_semaphore:
            # 确保目录存在
            dir_path = os.path.dirname(file_path)
            if dir_path and not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path, exist_ok=True)
                    logger.info(f"创建目录: {dir_path}")
                except Exception as e:
                    logger.error(f"创建目录失败: {dir_path}, 错误: {e}")
                    return False

            file_lock = self._get_file_lock(file_path)

            with file_lock:
                for attempt in range(max_retries):
                    try:
                        # 检查文件是否存在
                        file_exists = os.path.exists(file_path)
                        write_headers = False
                        
                        if file_exists:
                            try:
                                wb = openpyxl.load_workbook(file_path)
                                ws = wb.active
                                
                                if ws.max_row <= 1 and headers:
                                    write_headers = True
                            except Exception as e:
                                logger.warning(f"Excel文件可能损坏，将重新创建: {e}")
                                timestamp = int(time.time())
                                backup_path = f"{file_path}.backup_{timestamp}"
                                try:
                                    if os.path.exists(file_path):
                                        os.rename(file_path, backup_path)
                                        logger.info(f"已备份损坏文件到: {backup_path}")
                                except Exception as backup_e:
                                    logger.warning(f"备份文件失败: {backup_e}")
                                
                                wb = Workbook()
                                ws = wb.active
                                write_headers = True if headers else False
                        else:
                            wb = Workbook()
                            ws = wb.active
                            write_headers = True if headers else False
                        
                        # 写入表头
                        if write_headers and headers:
                            ws.append(headers)
                        
                        # 批量写入数据行
                        for data_row in data_rows:
                            ws.append(data_row)
                        
                        # 保存文件
                        try:
                            wb.save(file_path)
                        except PermissionError:
                            import uuid
                            temp_path = f"{file_path}.tmp_{uuid.uuid4().hex[:8]}"
                            wb.save(temp_path)
                            os.replace(temp_path, file_path)
                        
                        logger.debug(f"Excel批量写入成功: {file_path}, 写入{len(data_rows)}行")
                        return True
                    
                    except PermissionError as e:
                        logger.warning(f"Excel文件被占用，重试 {attempt + 1}/{max_retries}: {e}")
                        if attempt < max_retries - 1:
                            time.sleep(0.5 * (attempt + 1))
                        continue
                    
                    except Exception as e:
                        logger.error(f"Excel批量写入失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                        if attempt < max_retries - 1:
                            time.sleep(0.2)
                        continue
                
                logger.error(f"Excel批量写入失败，已重试 {max_retries} 次: {file_path}")
                return False

    def write_smart(self, file_path: str, data_row: List, headers: Optional[List] = None,
                   batch_size: Optional[int] = None) -> bool:
        """
        智能批量写入模式
        自动收集数据到指定批量大小后写入

        Args:
            file_path: Excel文件路径
            data_row: 数据行
            headers: 表头
            batch_size: 批量大小，None时使用默认值

        Returns:
            bool: 写入是否成功
        """
        if batch_size is None:
            batch_size = self.default_batch_size

        # 获取文件的批量缓存
        cache = self._get_batch_cache(file_path)

        with cache['lock']:
            # 设置表头（如果还没有设置）
            if cache['headers'] is None and headers:
                cache['headers'] = headers

            # 添加数据到缓存
            cache['data'].append(data_row)

            # 检查是否达到批量大小
            if len(cache['data']) >= batch_size:
                # 执行批量写入
                data_to_write = cache['data'].copy()
                headers_to_write = cache['headers']

                # 清空缓存
                cache['data'].clear()

                # 执行批量写入
                return self.write_batch(file_path, data_to_write, headers_to_write)

        return True  # 数据已缓存，等待批量写入

    def flush_cache(self, file_path: str = None) -> bool:
        """
        强制刷新缓存，写入所有待写入的数据

        Args:
            file_path: 指定文件路径，None时刷新所有缓存

        Returns:
            bool: 刷新是否成功
        """
        try:
            with self._batch_lock:
                if file_path:
                    # 刷新指定文件的缓存
                    if file_path in self._batch_cache:
                        cache = self._batch_cache[file_path]
                        with cache['lock']:
                            if cache['data']:
                                data_to_write = cache['data'].copy()
                                headers_to_write = cache['headers']
                                cache['data'].clear()

                                return self.write_batch(file_path, data_to_write, headers_to_write)
                else:
                    # 刷新所有缓存
                    success = True
                    for fp, cache in self._batch_cache.items():
                        with cache['lock']:
                            if cache['data']:
                                data_to_write = cache['data'].copy()
                                headers_to_write = cache['headers']
                                cache['data'].clear()

                                if not self.write_batch(fp, data_to_write, headers_to_write):
                                    success = False
                    return success
            return True
        except Exception as e:
            logger.error(f"刷新缓存失败: {e}")
            return False

    def write_hybrid(self, file_path: str, data_row: List, headers: Optional[List] = None,
                    threshold: int = 100) -> bool:
        """
        混合策略写入模式
        先写入CSV，达到阈值后转换为Excel

        Args:
            file_path: Excel文件路径
            data_row: 数据行
            headers: 表头
            threshold: 转换阈值（行数）

        Returns:
            bool: 写入是否成功
        """
        # 生成CSV临时文件路径
        csv_path = file_path.replace('.xlsx', '_temp.csv')

        # 先写入CSV
        write_header = not os.path.exists(csv_path)
        try:
            with open(csv_path, 'a', encoding='utf-8', newline='') as f:
                writer = csv.writer(f)
                if write_header and headers:
                    writer.writerow(headers)
                writer.writerow(data_row)
        except Exception as e:
            logger.error(f"CSV写入失败: {e}")
            return False

        # 检查是否达到转换阈值
        try:
            with open(csv_path, 'r', encoding='utf-8') as f:
                line_count = sum(1 for _ in f)

            if line_count >= threshold:
                # 转换为Excel（不使用追加模式，因为是第一次转换）
                success = self._convert_csv_to_excel(csv_path, file_path, remove_csv=True, append_mode=False)
                if success:
                    logger.info(f"已转换为Excel: {file_path} ({line_count}行)")
                return success
            else:
                return True  # CSV写入成功，等待达到阈值

        except Exception as e:
            logger.error(f"混合策略写入失败: {e}")
            return False

    def _convert_csv_to_excel(self, csv_path: str, excel_path: str, remove_csv: bool = True,
                             append_mode: bool = False) -> bool:
        """
        将CSV文件转换为Excel文件

        Args:
            csv_path: CSV文件路径
            excel_path: Excel文件路径
            remove_csv: 转换后是否删除CSV文件
            append_mode: 是否追加到现有Excel文件

        Returns:
            bool: 转换是否成功
        """
        if not EXCEL_AVAILABLE:
            logger.error("Excel功能不可用，请安装openpyxl库")
            return False

        try:
            # 尝试使用pandas进行转换（如果可用）
            try:
                import pandas as pd

                # 读取CSV文件
                df_new = pd.read_csv(csv_path, encoding='utf-8')

                if append_mode and os.path.exists(excel_path):
                    # 追加模式：读取现有Excel文件并合并
                    try:
                        df_existing = pd.read_excel(excel_path, engine='openpyxl')
                        df_combined = pd.concat([df_existing, df_new], ignore_index=True)
                        df_combined.to_excel(excel_path, index=False, engine='openpyxl')
                    except Exception as e:
                        logger.warning(f"追加模式失败，使用覆盖模式: {e}")
                        df_new.to_excel(excel_path, index=False, engine='openpyxl')
                else:
                    # 覆盖模式
                    df_new.to_excel(excel_path, index=False, engine='openpyxl')

            except ImportError:
                # pandas不可用，使用openpyxl
                logger.warning("pandas未安装，使用openpyxl进行转换")
                return self._convert_csv_to_excel_openpyxl(csv_path, excel_path, remove_csv, append_mode)

            # 删除CSV文件（如果需要）
            if remove_csv and os.path.exists(csv_path):
                os.remove(csv_path)

            logger.info(f"CSV转Excel成功: {csv_path} -> {excel_path}")
            return True

        except Exception as e:
            logger.error(f"CSV转Excel失败: {e}")
            return False

    def _convert_csv_to_excel_openpyxl(self, csv_path: str, excel_path: str, remove_csv: bool = True,
                                      append_mode: bool = False) -> bool:
        """
        使用openpyxl将CSV转换为Excel（pandas不可用时的备用方案）
        """
        try:
            if append_mode and os.path.exists(excel_path):
                # 追加模式：加载现有Excel文件
                wb = openpyxl.load_workbook(excel_path)
                ws = wb.active
            else:
                # 覆盖模式：创建新文件
                wb = Workbook()
                ws = wb.active

            with open(csv_path, 'r', encoding='utf-8', newline='') as csvfile:
                reader = csv.reader(csvfile)
                for row in reader:
                    ws.append(row)

            wb.save(excel_path)

            if remove_csv and os.path.exists(csv_path):
                os.remove(csv_path)

            logger.info(f"CSV转Excel成功(openpyxl): {csv_path} -> {excel_path}")
            return True

        except Exception as e:
            logger.error(f"CSV转Excel失败(openpyxl): {e}")
            return False

    def flush_cache(self, file_path: Optional[str] = None) -> bool:
        """
        刷新缓存，写入所有待处理数据

        Args:
            file_path: 指定文件路径，None时刷新所有缓存

        Returns:
            bool: 刷新是否成功
        """
        success = True

        if file_path:
            # 刷新指定文件的缓存
            if file_path in self._batch_cache:
                cache = self._batch_cache[file_path]
                with cache['lock']:
                    if cache['data']:
                        result = self.write_batch(file_path, cache['data'], cache['headers'])
                        cache['data'].clear()
                        success = success and result
        else:
            # 刷新所有缓存
            with self._batch_lock:
                files_to_flush = list(self._batch_cache.keys())

            for fp in files_to_flush:
                cache = self._batch_cache[fp]
                with cache['lock']:
                    if cache['data']:
                        result = self.write_batch(fp, cache['data'], cache['headers'])
                        cache['data'].clear()
                        success = success and result

        return success

    def finalize_hybrid_files(self, directory: Optional[str] = None) -> int:
        """
        完成混合策略文件的最终转换
        将所有剩余的CSV临时文件转换为Excel

        Args:
            directory: 指定目录，None时处理常见目录

        Returns:
            int: 转换的文件数量
        """
        converted_count = 0

        if directory:
            search_dirs = [directory]
        else:
            # 搜索常见的输出目录
            search_dirs = [".", "output", "test_output"]

        for search_dir in search_dirs:
            if not os.path.exists(search_dir):
                continue

            for filename in os.listdir(search_dir):
                if filename.endswith('_temp.csv'):
                    csv_path = os.path.join(search_dir, filename)
                    excel_filename = filename.replace('_temp.csv', '.xlsx')
                    excel_path = os.path.join(search_dir, excel_filename)

                    # 检查是否有数据需要转换
                    try:
                        with open(csv_path, 'r', encoding='utf-8') as f:
                            line_count = sum(1 for _ in f)

                        if line_count > 0:
                            success = self._convert_csv_to_excel(csv_path, excel_path, remove_csv=True, append_mode=False)
                            if success:
                                converted_count += 1
                                logger.info(f"最终转换: {excel_path} ({line_count}行)")

                    except Exception as e:
                        logger.warning(f"处理临时文件失败: {csv_path}, 错误: {e}")

        return converted_count

    def write(self, file_path: str, data_row: List, headers: Optional[List] = None,
             mode: ExcelWriteMode = ExcelWriteMode.SMART, **kwargs) -> bool:
        """
        统一的写入接口

        Args:
            file_path: Excel文件路径
            data_row: 数据行
            headers: 表头
            mode: 写入模式
            **kwargs: 其他参数

        Returns:
            bool: 写入是否成功
        """
        if mode == ExcelWriteMode.DIRECT:
            return self.write_direct(file_path, data_row, headers, kwargs.get('max_retries', 3))
        elif mode == ExcelWriteMode.BATCH:
            # 批量模式需要外部调用write_batch
            logger.warning("批量模式请直接调用write_batch方法")
            return False
        elif mode == ExcelWriteMode.SMART:
            return self.write_smart(file_path, data_row, headers, kwargs.get('batch_size'))
        elif mode == ExcelWriteMode.HYBRID:
            return self.write_hybrid(file_path, data_row, headers, kwargs.get('threshold', 100))
        else:
            logger.error(f"不支持的写入模式: {mode}")
            return False

    async def write_async(self, file_path: str, data_row: List, headers: Optional[List] = None,
                         mode: ExcelWriteMode = ExcelWriteMode.SMART, **kwargs) -> bool:
        """
        异步写入接口

        Args:
            file_path: Excel文件路径
            data_row: 数据行
            headers: 表头
            mode: 写入模式
            **kwargs: 其他参数

        Returns:
            bool: 写入是否成功
        """
        try:
            # 获取文件级别的异步锁
            file_lock = await self._get_async_file_lock(file_path)

            # 使用异步锁确保同一文件的写入顺序
            async with file_lock:
                # 获取线程池
                executor = self._get_thread_pool()

                # 在专用线程池中执行同步的Excel写入操作
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    executor,
                    self.write,
                    file_path,
                    data_row,
                    headers,
                    mode,
                    **kwargs
                )

                # 添加小延迟确保文件操作完成
                await asyncio.sleep(0.01)

                return result

        except Exception as e:
            logger.error(f"异步Excel写入失败: {file_path}, 错误: {e}")
            return False

    def cleanup(self):
        """清理资源"""
        if self._thread_pool:
            self._thread_pool.shutdown(wait=True)

        # 刷新所有缓存
        self.flush_cache()

        logger.info("ExcelWriter资源清理完成")

    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except:
            pass


# 全局Excel写入器实例
_global_excel_writer = None


def get_excel_writer() -> ExcelWriter:
    """获取全局Excel写入器实例"""
    global _global_excel_writer
    if _global_excel_writer is None:
        _global_excel_writer = ExcelWriter()
    return _global_excel_writer
