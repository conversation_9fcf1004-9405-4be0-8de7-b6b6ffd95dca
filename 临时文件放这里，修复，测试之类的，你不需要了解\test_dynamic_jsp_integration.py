#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试JSP处理器整合到动态翻页模块
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from gui.crawler_thread import CrawlerThread

def test_dynamic_jsp_integration():
    """测试动态翻页模式下的JSP处理器"""
    print("🧪 测试JSP处理器整合到动态翻页模块...")
    
    # 合肥政协网站测试URL
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    print(f"📋 测试URL: {test_url}")
    
    # 配置动态翻页参数
    config = {
        'input_url': test_url,
        'max_pages': 2,  # 测试2页
        'headless': True,
        'disable_js_injection': False,
        'mode': 'safe',
        'file_format': 'CSV',
        'export_filename': 'test_dynamic_jsp',
        'classid': 'test',
        'max_workers': 2,
        'retry': 1,
        'interval': 1,
        'use_module_config': True,
        'config_group': 'test_group',
        
        # 动态翻页配置
        'pagination_config': {
            'pagination_type': 'JSP动态翻页',
            'next_button_selector': 'a.next:not(.lose)'
        },
        
        # 内容选择器配置
        'content_selectors': [
            '.article_cont', '.content', '.article', '.main-content',
            'div[class*="content"]', 'div[class*="article"]', 
            '.TRS_Editor', '.view', '.zhengwen', '.text'
        ],
        'title_selectors': [
            'h1', 'h2', '.title', '.article-title', '.news-title'
        ],
        'date_selectors': [
            '.date', '.time', '.publish-time', '.create-time'
        ]
    }
    
    # 创建爬虫线程
    crawler_thread = CrawlerThread(config)
    
    # 设置日志回调
    def log_handler(message):
        print(f"[LOG] {message}")
    
    crawler_thread.log_signal.connect(log_handler)
    
    # 设置进度回调
    def progress_handler(current, total):
        print(f"[PROGRESS] {current}/{total}")
    
    crawler_thread.progress_signal.connect(progress_handler)
    
    print(f"\n🚀 开始动态翻页测试...")
    
    try:
        # 运行动态翻页
        result = crawler_thread.run_dynamic_pagination()
        
        print(f"\n✅ 动态翻页测试结果:")
        print(f"   - 总计: {result.get('total', 0)}")
        print(f"   - 成功: {result.get('success', 0)}")
        print(f"   - 失败: {result.get('failed', 0)}")
        print(f"   - 保存目录: {result.get('save_dir', '未知')}")
        print(f"   - JSP处理: {'✅' if result.get('jsp_processed', False) else '❌'}")
        
        if result.get('jsp_processed', False):
            print(f"🎯 JSP处理器成功整合到动态翻页模块！")
        else:
            print(f"⚠️ 未使用JSP处理器，可能回退到传统动态翻页")
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

def test_traditional_vs_dynamic_jsp():
    """对比传统翻页和动态翻页JSP处理的效果"""
    print("\n🔄 对比测试：传统翻页 vs 动态翻页JSP处理...")
    
    test_url = "http://www.hfszx.org.cn/hfzx/web/list.jsp?strWebSiteId=1354153871125000&strColId=1508142920296001"
    
    # 基础配置
    base_config = {
        'input_url': test_url,
        'max_pages': 1,  # 只测试1页
        'headless': True,
        'disable_js_injection': False,
        'mode': 'safe',
        'file_format': 'CSV',
        'max_workers': 2,
        'retry': 1,
        'interval': 1,
        'use_module_config': True
    }
    
    def log_handler(message):
        print(f"[COMPARE] {message}")
    
    def progress_handler(current, total):
        print(f"[PROGRESS] {current}/{total}")
    
    try:
        # 测试1：传统翻页模式
        print("\n📊 测试1：传统翻页模式")
        traditional_config = base_config.copy()
        traditional_config.update({
            'export_filename': 'test_traditional_jsp',
            'config_group': 'traditional_test'
        })
        
        traditional_thread = CrawlerThread(traditional_config)
        traditional_thread.log_signal.connect(log_handler)
        traditional_thread.progress_signal.connect(progress_handler)
        
        traditional_result = traditional_thread.run_traditional_crawling()
        print(f"   传统翻页结果: 总计{traditional_result.get('total', 0)}, 成功{traditional_result.get('success', 0)}")
        
        # 测试2：动态翻页模式（JSP处理器）
        print("\n📊 测试2：动态翻页模式（JSP处理器）")
        dynamic_config = base_config.copy()
        dynamic_config.update({
            'export_filename': 'test_dynamic_jsp',
            'config_group': 'dynamic_test',
            'pagination_config': {
                'pagination_type': 'JSP动态翻页'
            }
        })
        
        dynamic_thread = CrawlerThread(dynamic_config)
        dynamic_thread.log_signal.connect(log_handler)
        dynamic_thread.progress_signal.connect(progress_handler)
        
        dynamic_result = dynamic_thread.run_dynamic_pagination()
        print(f"   动态翻页结果: 总计{dynamic_result.get('total', 0)}, 成功{dynamic_result.get('success', 0)}")
        
        # 对比结果
        print(f"\n📈 对比结果:")
        print(f"   - 传统翻页: 总计{traditional_result.get('total', 0)}, 成功{traditional_result.get('success', 0)}")
        print(f"   - 动态翻页: 总计{dynamic_result.get('total', 0)}, 成功{dynamic_result.get('success', 0)}")
        print(f"   - JSP处理: {'✅' if dynamic_result.get('jsp_processed', False) else '❌'}")
        
        if dynamic_result.get('success', 0) > traditional_result.get('success', 0):
            print(f"   ✅ 动态翻页JSP处理效果更好！")
        elif traditional_result.get('success', 0) > dynamic_result.get('success', 0):
            print(f"   ⚠️ 传统翻页效果更好")
        else:
            print(f"   🤔 两种方式效果相当")
        
    except Exception as e:
        print(f"❌ 对比测试出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始JSP处理器动态翻页集成测试...")
    
    # 运行主要测试
    test_dynamic_jsp_integration()
    
    # 运行对比测试
    test_traditional_vs_dynamic_jsp()
    
    print("\n🎯 所有测试完成！")
