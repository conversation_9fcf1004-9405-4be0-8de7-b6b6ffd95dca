#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自动命名完整流程
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_flow():
    """测试完整的自动命名流程"""
    print("🧪 测试自动命名完整流程...")
    print("=" * 60)
    
    try:
        from gui.main_window import CrawlerGUI
        from gui.config_manager import GUIConfigManager
        from PyQt5.QtWidgets import QApplication
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建GUI实例
        gui = CrawlerGUI()
        
        # 设置测试数据
        print("📋 设置测试数据...")
        gui.parent_category_combo.addItem("政府机构")
        gui.parent_category_combo.setCurrentText("政府机构")
        
        gui.sub_category_combo.addItem("政协系统")
        gui.sub_category_combo.setCurrentText("政协系统")
        
        gui.child_category_combo.addItem("合肥政协")
        gui.child_category_combo.setCurrentText("合肥政协")
        
        gui.config_combo.addItem("参言建议")
        gui.config_combo.setCurrentText("参言建议")
        
        # 设置自动命名模式
        gui.filename_mode_combo.setCurrentText("自动命名")
        
        # 测试步骤1：生成文件名
        print("\n🔍 步骤1：测试文件名生成...")
        filename = gui.generate_filename_from_category_path()
        print(f"   生成的文件名: '{filename}'")
        
        # 测试步骤2：获取有效文件名
        print("\n🔍 步骤2：测试有效文件名获取...")
        effective_filename = gui.get_effective_export_filename()
        print(f"   有效文件名: '{effective_filename}'")
        
        # 测试步骤3：GUI配置获取
        print("\n🔍 步骤3：测试GUI配置获取...")
        config_data = gui.get_config_from_gui()
        export_filename = config_data.get('export_filename', '')
        print(f"   GUI配置中的export_filename: '{export_filename}'")
        print(f"   export_filename类型: {type(export_filename)}")
        print(f"   export_filename是否为空: {not export_filename}")
        
        # 测试步骤4：爬虫配置准备
        print("\n🔍 步骤4：测试爬虫配置准备...")
        gui_config_manager = GUIConfigManager()
        crawler_config = gui_config_manager.prepare_crawler_config(config_data)
        crawler_export_filename = crawler_config.get('export_filename', '')
        print(f"   爬虫配置中的export_filename: '{crawler_export_filename}'")
        print(f"   爬虫配置export_filename类型: {type(crawler_export_filename)}")
        print(f"   爬虫配置export_filename是否为None: {crawler_export_filename is None}")
        
        # 测试步骤5：模拟爬虫文件名判断
        print("\n🔍 步骤5：模拟爬虫文件名判断...")
        if crawler_export_filename:
            print(f"   ✅ 爬虫将使用文件名: {crawler_export_filename}.xlsx")
        else:
            print(f"   ❌ 爬虫将使用页面标题作为文件名")
        
        # 总结
        print("\n📊 测试结果总结:")
        print(f"   1. 文件名生成: {'✅' if filename == '合肥政协_参言建议' else '❌'} ({filename})")
        print(f"   2. 有效文件名: {'✅' if effective_filename == '合肥政协_参言建议' else '❌'} ({effective_filename})")
        print(f"   3. GUI配置传递: {'✅' if export_filename == '合肥政协_参言建议' else '❌'} ({export_filename})")
        print(f"   4. 爬虫配置接收: {'✅' if crawler_export_filename == '合肥政协_参言建议' else '❌'} ({crawler_export_filename})")
        print(f"   5. 文件名判断: {'✅' if crawler_export_filename else '❌'}")
        
        # 检查是否所有步骤都正确
        all_correct = (
            filename == '合肥政协_参言建议' and
            effective_filename == '合肥政协_参言建议' and
            export_filename == '合肥政协_参言建议' and
            crawler_export_filename == '合肥政协_参言建议' and
            bool(crawler_export_filename)
        )
        
        if all_correct:
            print("\n🎉 所有测试通过！自动命名流程正常工作")
        else:
            print("\n❌ 存在问题，需要进一步调试")
            
        return all_correct
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_p_tag_issue():
    """测试P标签问题的具体情况"""
    print("\n🧪 测试P标签内容判空问题...")
    print("=" * 60)
    
    try:
        # 模拟P标签内容
        test_contents = [
            "合肥市政协以在全体委员中开展的双联双创工作为抓手，积极拓宽服务基层、服务群众的新渠道，促进政协委员履职和本职工作双创新。",
            "合肥市政协主办 版权所有",
            "电话：0551-63538200"
        ]
        
        print(f"📝 模拟P标签内容数量: {len(test_contents)}")
        for i, content in enumerate(test_contents):
            print(f"   P{i+1}: {content[:50]}...")
        
        # 测试判空逻辑
        from core.crawler import is_content_empty, is_all_selectors_content_empty
        
        # 合并内容
        combined_content = "\n".join(test_contents)
        print(f"\n📏 合并后内容长度: {len(combined_content)} 字符")
        
        # 测试判空
        is_empty_single = is_content_empty(combined_content)
        is_empty_all = is_all_selectors_content_empty(test_contents)
        
        print(f"🔍 单个内容判空结果: {is_empty_single}")
        print(f"🔍 所有选择器判空结果: {is_empty_all}")
        
        if not is_empty_all:
            print("✅ P标签内容判空正常")
            return True
        else:
            print("❌ P标签内容被错误判为空")
            return False
            
    except Exception as e:
        print(f"❌ P标签测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 开始完整流程测试...")
    print("=" * 80)
    
    # 测试1：自动命名流程
    naming_ok = test_complete_flow()
    
    # 测试2：P标签判空
    p_tag_ok = test_p_tag_issue()
    
    print("\n" + "=" * 80)
    print("🏁 测试完成")
    print(f"   自动命名流程: {'✅ 正常' if naming_ok else '❌ 异常'}")
    print(f"   P标签判空: {'✅ 正常' if p_tag_ok else '❌ 异常'}")
    
    if naming_ok and p_tag_ok:
        print("\n🎉 所有问题已解决！")
    else:
        print("\n⚠️ 仍有问题需要解决")

if __name__ == "__main__":
    main()
