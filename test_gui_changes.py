#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI修改的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_import():
    """测试导入是否正常"""
    try:
        from gui.main_window import CrawlerGUI
        print("✅ GUI模块导入成功")
        return True
    except Exception as e:
        print(f"❌ GUI模块导入失败: {e}")
        return False

def test_articles_directory():
    """测试articles目录创建"""
    try:
        import os
        current_dir = os.path.dirname(os.path.abspath(__file__))
        articles_dir = os.path.join(current_dir, "articles")
        
        if not os.path.exists(articles_dir):
            os.makedirs(articles_dir)
            print(f"✅ 创建articles目录: {articles_dir}")
        else:
            print(f"✅ articles目录已存在: {articles_dir}")
        
        return True
    except Exception as e:
        print(f"❌ articles目录处理失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试GUI修改...")
    print("=" * 50)
    
    # 测试导入
    if not test_import():
        return False
    
    # 测试目录创建
    if not test_articles_directory():
        return False
    
    print("=" * 50)
    print("✅ 所有测试通过！")
    print("\n📋 修改总结:")
    print("1. ✅ 添加了'📂 打开目录'按钮")
    print("2. ✅ 默认文件格式改为Excel")
    print("3. ✅ 默认保存路径为articles目录")
    print("4. ✅ 实现了打开导出目录功能")
    
    return True

if __name__ == "__main__":
    main()
