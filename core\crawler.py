import asyncio
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>rowser<PERSON>ontext
from typing import Optional, Dict, Any, Union, Callable, Awaitable
import logging
import sys
import time
import os
import csv
import random
import requests
from utils import text_cleaner
from utils.text_cleaner import normalize_url_for_deduplication
from bs4 import BeautifulSoup
from urllib.parse import urljoin
import re
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading
import queue
import urllib.parse as urlparse
from .field_extractor import get_field_extractor, configure_fields, add_extended_fields



# 导入模组管理器
try:
    from modules.manager import get_config_for_url, match_module_for_url
    USE_MODULE_MANAGER = True
except ImportError:
    USE_MODULE_MANAGER = False
    print("警告: 无法导入模组管理器，将使用传统配置方式")

# 导入健壮等待策略
try:
    from utils.robust_wait_strategy import robust_goto
    ROBUST_WAIT_AVAILABLE = True
    print("✅ 健壮等待策略已加载")
except ImportError as e:
    ROBUST_WAIT_AVAILABLE = False
    print(f"⚠️ 健壮等待策略不可用: {e}")

# 导入JSP处理器
try:
    from modules.jsp_website_handler import jsp_handler
    JSP_HANDLER_AVAILABLE = True
    print("✅ JSP处理器已加载")
except ImportError as e:
    JSP_HANDLER_AVAILABLE = False
    print(f"⚠️ JSP处理器不可用: {e}")

    # 提供回退函数
    async def robust_goto(page: Page, url: str, **kwargs) -> bool:
        """回退的页面访问函数"""
        try:
            timeout = kwargs.get('timeout', 90000)
            wait_until = kwargs.get('preferred_strategy', 'networkidle')
            await page.goto(url, timeout=timeout, wait_until=wait_until)
            await page.wait_for_load_state('networkidle', timeout=45000)
            await asyncio.sleep(2)
            return True
        except Exception as e:
            logger.error(f"页面访问失败: {e}")
            return False

# 导入Excel写入器
try:
    from .excel_writer import ExcelWriter, ExcelWriteMode, get_excel_writer
    EXCEL_WRITER_AVAILABLE = True
    EXCEL_AVAILABLE = True  # 保持向后兼容
except ImportError:
    EXCEL_WRITER_AVAILABLE = False
    EXCEL_AVAILABLE = False
    print("警告: Excel写入器不可用，Excel格式不可用。")

# 导入优化的Playwright配置
try:
    from utils.playwright_config import OptimizedPlaywrightConfig
    OPTIMIZED_CONFIG_AVAILABLE = True
except ImportError:
    OPTIMIZED_CONFIG_AVAILABLE = False
    print("警告: playwright_optimized_config.py 未找到，使用默认配置")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('CrawlerPlaywright')



print("Loaded crawler_new.py from:", __file__)


# ==================== 统一数据写入函数 ====================

async def write_article_data_async(file_path, data_row, headers, file_format="CSV", strategy="auto"):
    """
    统一的异步数据写入函数，支持CSV和Excel格式，以及多种优化策略

    Args:
        file_path: 文件路径
        data_row: 数据行
        headers: 表头
        file_format: 文件格式 ("CSV" 或 "EXCEL")
        strategy: 写入策略 ("auto", "smart", "batch", "hybrid", "direct")
    """
    try:
        if file_format.upper() == "EXCEL":
            if EXCEL_WRITER_AVAILABLE:
                # 使用新的ExcelWriter类
                excel_writer = get_excel_writer()

                # 映射策略到ExcelWriteMode
                if strategy == "direct":
                    mode = ExcelWriteMode.DIRECT
                elif strategy == "batch":
                    mode = ExcelWriteMode.BATCH
                elif strategy == "hybrid":
                    mode = ExcelWriteMode.HYBRID
                else:  # "auto" 或 "smart"
                    mode = ExcelWriteMode.SMART

                result = await excel_writer.write_async(file_path, data_row, headers, mode)

                # 如果是智能模式，立即刷新缓存确保数据写入
                if mode == ExcelWriteMode.SMART:
                    excel_writer.flush_cache(file_path)

                return result
            else:
                # Excel写入器不可用，返回错误
                logger.error("Excel写入器不可用，无法写入Excel文件")
                return False
        else:
            # CSV写入
            def write_csv():
                write_header = not os.path.exists(file_path)
                with open(file_path, 'a', encoding='utf-8', newline='') as f:
                    writer = csv.writer(f)
                    if write_header:
                        writer.writerow(headers)
                    writer.writerow(data_row)

            await asyncio.to_thread(write_csv)
            return True
    except Exception as e:
        logger.error(f"写入数据失败: {e}")
        return False



# ==================== 配置合并函数 ====================

def merge_module_config(link, base_config):
    """
    统一的模组配置合并函数，避免重复代码

    Args:
        link: 文章URL
        base_config: 基础配置字典

    Returns:
        合并后的配置字典
    """
    if not USE_MODULE_MANAGER:
        return base_config

    try:
        module_config = get_config_for_url(link)
        if module_config:
            module_name = match_module_for_url(link)
            logger.info(f"使用模组配置: {module_name}")

            # 合并配置
            merged_config = base_config.copy()
            merged_config.update(module_config)
            return merged_config
    except Exception as e:
        logger.warning(f"使用模组配置失败，回退到传统配置: {e}")

    return base_config


# ==================== 浏览器启动函数 ====================

async def launch_browser(
    p,  # async_playwright 实例
    headless: bool = True,  # 默认改为True，符合爬虫场景
    browser_type: str = "chromium",
    slow_mo: int = 100,
    proxy: Optional[Dict] = None,
    viewport: Dict[str, int] = {"width": 1920, "height": 1080},  # 增大默认视口
    user_agent: Optional[str] = None,
    disable_js_injection: bool = False,  # 新增：是否禁用JS注入
    **launch_kwargs
) -> tuple[Browser, BrowserContext, Page]:
    """
    启动Playwright浏览器（优化版）
    :param p: async_playwright 实例
    :param headless: 是否无头模式（默认True）
    :param browser_type: 浏览器类型 (chromium, firefox, webkit)
    :param slow_mo: 操作延迟(毫秒)
    :param proxy: 代理设置 {'server': 'http://host:port'}
    :param viewport: 视口大小 {'width': 1920, 'height': 1080}
    :param user_agent: 自定义User-Agent
    :param disable_js_injection: 是否禁用JS注入（避免影响网站JS加载）
    :return: (browser, context, page)
    """
    # 使用优化配置（如果可用）
    if OPTIMIZED_CONFIG_AVAILABLE:
        config = OptimizedPlaywrightConfig()

        if not user_agent:
            user_agent = config.get_random_user_agent()

        # 使用优化的浏览器启动参数
        optimized_args = config.BROWSER_ARGS
    else:
        # 回退到默认配置
        default_user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]

        if not user_agent:
            user_agent = random.choice(default_user_agents)

        # 默认的浏览器启动参数
        optimized_args = [
            "--no-sandbox",
            "--disable-setuid-sandbox",
            "--disable-dev-shm-usage",
            "--disable-accelerated-2d-canvas",
            "--no-first-run",
            "--no-zygote",
            "--disable-gpu",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-features=TranslateUI",
            "--disable-ipc-flooding-protection",
            "--disable-extensions"
        ]

        # 只有在不禁用JS注入时才添加这些可能影响JS的参数
        if not disable_js_injection:
            optimized_args.extend([
                "--disable-blink-features=AutomationControlled",
                "--exclude-switches=enable-automation"
            ])

    # 选择浏览器类型
    browser_launcher = getattr(p, browser_type).launch

    # 准备启动参数（使用优化配置）
    launch_options = {
        "headless": headless,
        "slow_mo": slow_mo,
        "args": optimized_args,
        **launch_kwargs
    }

    # 添加代理设置
    if proxy:
        launch_options["proxy"] = proxy

    browser = await browser_launcher(**launch_options)

    # 优化的HTTP请求头
    extra_headers = {
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Accept-Encoding": "gzip, deflate, br",
        "Cache-Control": "no-cache",
        "Pragma": "no-cache",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "none",
        "Sec-Fetch-User": "?1",
        "Upgrade-Insecure-Requests": "1"
    }

    # 创建上下文（优化版）
    context_options = {
        "viewport": viewport,
        "extra_http_headers": extra_headers,
        "java_script_enabled": True,
        "ignore_https_errors": True,
        "locale": "zh-CN",
        "timezone_id": "Asia/Shanghai"
    }

    # 只有在不禁用JS注入时才设置bypass_csp
    if not disable_js_injection:
        context_options["bypass_csp"] = True

    if user_agent:
        context_options["user_agent"] = user_agent

    context = await browser.new_context(**context_options)

    # 根据参数决定是否注入反检测脚本
    if not disable_js_injection:
        # 注入反检测脚本（可能影响某些网站的JS加载）
        try:
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
                window.chrome = { runtime: {} };
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)
        except Exception as e:
            logger.warning(f"JS反检测脚本注入失败: {e}")

    page = await context.new_page()

    return browser, context, page

async def save_failed_url_async(link, reason, save_dir, export_filename=None, article_title="", file_format="CSV"):
    """
    异步版本的保存失败URL函数 - 直接异步实现

    Args:
        link: 失败的URL
        reason: 失败原因
        save_dir: 保存目录
        export_filename: 导出文件名（可选）
        article_title: 文章标题
        file_format: 文件格式

    Returns:
        bool: 是否保存成功
    """
    try:
        # 构建失败文件路径 - 失败URL只保存为CSV格式
        if export_filename:
            failed_filename = f"{export_filename}_failed.csv"
            failed_file_path = os.path.join(save_dir, failed_filename)
        else:
            # 使用默认的失败文件名 - 只保存为CSV
            failed_file_path = os.path.join(save_dir, "failed_urls.csv")

        # 准备失败记录数据
        now_str = time.strftime('%Y-%m-%d %H:%M:%S')
        failed_data_row = [
            now_str,           # 失败时间
            link,              # 失败的URL
            article_title,     # 文章标题（如果有）
            reason,            # 失败原因
            "待重试"           # 状态
        ]
        failed_headers = ['failed_time', 'failed_url', 'title', 'reason', 'status']

        # 使用统一的异步数据写入函数 - 失败URL只保存为CSV格式
        success = await write_article_data_async(failed_file_path, failed_data_row, failed_headers, "CSV")

        if success:
            logger.info(f"已记录失败URL: {link} - {reason}")
        else:
            logger.error(f"记录失败URL时出错: {link}")

        return success

    except Exception as e:
        logger.error(f"异步保存失败URL时出错: {link}, 错误: {str(e)}")
        return False

def is_content_empty(content_text):
    """
    检查内容是否为空

    Args:
        content_text: 文章内容

    Returns:
        bool: 内容是否为空
    """
    if not content_text:
        return True

    # 去除空白字符后检查
    cleaned_content = content_text.strip()
    if not cleaned_content:
        return True

    # 检查是否只包含HTML标签或特殊字符
    import re
    # 移除HTML标签
    text_only = re.sub(r'<[^>]+>', '', cleaned_content)
    # 移除空白字符和常见的无意义字符
    text_only = re.sub(r'[\s\n\r\t\u00a0\u3000]+', '', text_only)

    # 如果清理后的内容长度小于10个字符，认为是空内容
    return len(text_only) < 10

def is_all_selectors_content_empty(content_results):
    """
    检查所有选择器提取的内容集合是否为空

    Args:
        content_results: 所有选择器提取的内容列表

    Returns:
        bool: 所有内容是否都为空
    """
    if not content_results:
        return True

    # 合并所有选择器的内容
    combined_content = ""
    for content in content_results:
        if content and content.strip():
            combined_content += content.strip() + "\n"

    # 检查合并后的内容是否为空
    return is_content_empty(combined_content)

# 创建保存文章的目录
def create_save_dir(page_title, export_filename=None):
    base_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "articles")
    if not os.path.exists(base_dir):
        os.makedirs(base_dir)
    if export_filename:
        # 只返回articles目录，不创建子文件夹
        return base_dir
    page_folder = "".join(c for c in page_title if c.isalnum() or c in (' ','-','_'))
    save_dir = os.path.join(base_dir, page_folder)
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    return save_dir

# 获取文章链接 (Playwright版本)
async def get_article_links_playwright(page: Page, input_url, list_container_selector, article_item_selector, title_selectors=None):
    try:
        # 使用健壮等待策略访问页面
        logger.info(f"使用健壮等待策略访问列表页: {input_url}")
        success = await robust_goto(page, input_url)

        if not success:
            raise RuntimeError(f"无法访问目标URL: {input_url}")

        logger.info(f"✅ 列表页访问成功: {input_url}")

    except Exception as e:
        raise RuntimeError(f"无法访问目标URL: {input_url}，错误: {e}")

    page_title = await page.title()
    logger.info(f"正在爬取页面：{page_title}")

    try:
        # 等待页面加载
        await page.wait_for_load_state('networkidle', timeout=10000)

        # 支持多个列表容器
        main_containers = await page.query_selector_all(list_container_selector)

        if not main_containers:
            raise RuntimeError(f"页面结构异常，未找到列表容器: {list_container_selector}")

    except Exception as e:
        raise RuntimeError(f"页面结构异常，未找到列表容器: {list_container_selector}，错误: {e}")

    article_elements = []
    article_links = []
    article_titles = []

    for container in main_containers:
        try:
            articles = await container.query_selector_all(article_item_selector)

            article_elements.extend(articles)

            for article in articles:
                # 优先取 article 本身的 href
                href = await article.get_attribute('href')

                # 若没有，再取其下第一个 a 标签的 href
                if not href:
                    try:
                        a_tag = await article.query_selector('a')
                        if a_tag:
                            href = await a_tag.get_attribute('href')
                    except Exception:
                        href = None

                # 如果还是没有，尝试从 onclick 提取
                if not href:
                    onclick = await article.get_attribute('onclick')
                    if onclick:
                        # 兼容 onclick="location='url'" 或 onclick="window.location='url'"
                        patterns = [
                            r"location(?:\.href)?\s*=\s*['\"]([^'\"]+)",  # location='url'
                            r"window\.open\s*\(\s*['\"]([^'\"]+)",       # window.open('url'
                            r"window\.location\.href\s*=\s*['\"]([^'\"]+)",  # window.location.href='url'
                            r"redirectTo\s*\(\s*['\"]([^'\"]+)"           # 自定义函数如 redirectTo('url'
                        ]

                        for pattern in patterns:
                            match = re.search(pattern, onclick)
                            if match:
                                href = match.group(1)
                                break

                # 获取文章标题
                title_text = ""
                # 如果没有专门的标题选择器或提取失败，使用文章元素的文本
                if not title_text:
                    title_text = await article.text_content()

                if title_text:
                    title_text = title_text.strip()

                # 只要有链接就加入，保证一一对应
                article_links.append(href)
                article_titles.append(title_text)

        except Exception:
            continue

    logger.info(f"找到 {len(article_links)} 个有效链接")
    return article_elements, page_title, article_links, article_titles

# 获取完整的链接
def get_full_link(href, input_url, base_url, url_mode):
    """
    修复版URL拼接函数

    Args:
        href: 原始链接
        input_url: 输入URL（当前页面URL）
        base_url: 基础URL
        url_mode: URL模式 ('absolute'/'relative')

    Returns:
        str: 完整的URL
    """
    if not href:
        return ''

    # 绝对URL，直接返回
    if href.startswith(('http://', 'https://')):
        return href

    # 协议相对URL
    if href.startswith('//'):
        # 从input_url或base_url中提取协议
        if input_url and '://' in input_url:
            scheme = input_url.split('://')[0]
        elif base_url and '://' in base_url:
            scheme = base_url.split('://')[0]
        else:
            scheme = 'https'
        return f"{scheme}:{href}"

    # 锚点
    if href.startswith('#'):
        return urljoin(input_url or base_url, href)

    # 相对路径处理 - 优先使用input_url
    base_for_join = input_url if input_url else base_url
    if not base_for_join:
        return href  # 如果没有基础URL，返回原始href

    # 确保base_for_join是完整的URL
    if not base_for_join.startswith(('http://', 'https://')):
        return href

    result = urljoin(base_for_join, href)
    return result

# 简化的翻页URL生成函数（替代旧的翻页处理模块）
def get_page_url(input_url, page_num, page_suffix, base_url, max_page=None, start_page=1):
    """
    根据页码生成对应的页面URL（改进版本），支持最大翻页数、无限制和自定义起始页数。

    Args:
        input_url: 输入URL
        page_num: 页码
        page_suffix: 翻页后缀
        base_url: 基础URL
        max_page: 最大翻页数（None或0或float('inf')为无限制）
        start_page: 起始页数（默认为1，支持从0、2等开始）

    Returns:
        生成的页面URL或None（超出最大页数时）
    """
    from urllib.parse import urljoin, urlparse, urlunparse

    # 支持无限制翻页：max_page=None/0/float('inf')时不限制
    if max_page not in (None, 0, float('inf')):
        if page_num > max_page:
            return None

    # 如果是起始页，返回原始URL
    if page_num == start_page:
        return input_url

    # 处理翻页后缀中的占位符
    if "{n}" in page_suffix:
        suffix = page_suffix.replace("{n}", str(page_num))
    else:
        suffix = page_suffix

    # 解析URL结构
    parsed_url = urlparse(base_url)

    # 如果后缀以/开头，说明是绝对路径
    if suffix.startswith('/'):
        # 替换整个路径
        new_parsed = parsed_url._replace(path=suffix)
        return urlunparse(new_parsed)

    # 如果输入URL的路径以/结尾，直接拼接
    if parsed_url.path.endswith('/'):
        new_path = parsed_url.path + suffix
    else:
        # 如果路径不以/结尾，需要添加/
        new_path = parsed_url.path + '/' + suffix

    # 重新构建URL
    new_parsed = parsed_url._replace(path=new_path)
    return urlunparse(new_parsed)

# ==================== 健壮的来源提取函数 ====================

async def _extract_source_robustly(page, source_selectors, log_callback=None):
    """
    健壮的来源提取函数，使用多种策略确保准确提取来源信息

    Args:
        page: Playwright页面对象
        source_selectors: 来源选择器列表
        log_callback: 日志回调函数

    Returns:
        str: 提取到的来源信息
    """
    source = ""

    # 策略1: 使用配置的选择器进行提取
    if source_selectors:
        for source_sel in source_selectors:
            if not source_sel:
                continue

            try:
                source_elems = await page.query_selector_all(source_sel)

                # 尝试所有匹配的元素，而不只是第一个或第二个
                for i, elem in enumerate(source_elems):
                    try:
                        source_text = await elem.text_content()
                        if source_text:
                            source_text = source_text.strip()

                            # 使用增强的来源提取函数
                            extracted_source = text_cleaner.extract_source_from_text(source_text)

                            if extracted_source and extracted_source != "本站" and len(extracted_source) > 1:
                                source = text_cleaner.normalize_source(extracted_source)
                                if log_callback:
                                    log_callback(f"✓ 选择器 {source_sel}[{i}] 提取到来源: {source}")
                                break

                    except Exception as e:
                        if log_callback:
                            log_callback(f"⚠️ 处理选择器 {source_sel}[{i}] 时出错: {e}")
                        continue

                # 如果找到有效来源，停止尝试其他选择器
                if source and source != "本站":
                    break

            except Exception as e:
                if log_callback:
                    log_callback(f"⚠️ 选择器 {source_sel} 执行失败: {e}")
                continue

    # 策略2: 如果选择器提取失败，尝试从页面HTML中智能提取
    if not source or source == "本站":
        try:
            # 获取页面的HTML内容
            html_content = await page.content()

            # 使用智能文本分析提取来源
            extracted_source = text_cleaner.extract_source_from_text(html_content)

            if extracted_source and extracted_source != "本站" and len(extracted_source) > 1:
                source = text_cleaner.normalize_source(extracted_source)
                if log_callback:
                    log_callback(f"✓ 智能分析提取到来源: {source}")

        except Exception as e:
            if log_callback:
                log_callback(f"⚠️ 智能来源提取失败: {e}")

    # 策略3: 尝试从特定的常见位置提取来源
    if not source or source == "本站":
        common_selectors = [
            '.article_date',
            '.source',
            '.info-source',
            '.article-source',
            '.article-info',
            '.meta-source',
            '[class*="source"]',
            '[class*="来源"]',
            'script[type="text/javascript"]'
        ]

        for selector in common_selectors:
            try:
                elems = await page.query_selector_all(selector)
                for elem in elems:
                    try:
                        if selector.startswith('script'):
                            # 对于script标签，获取innerHTML
                            content = await elem.inner_html()
                        else:
                            content = await elem.text_content()

                        if content:
                            extracted_source = text_cleaner.extract_source_from_text(content)
                            if extracted_source and extracted_source != "本站" and len(extracted_source) > 1:
                                source = text_cleaner.normalize_source(extracted_source)
                                if log_callback:
                                    log_callback(f"✓ 通用选择器 {selector} 提取到来源: {source}")
                                break

                    except Exception:
                        continue

                if source and source != "本站":
                    break

            except Exception:
                continue

    # 最终处理
    if not source:
        source = "本站"
        if log_callback:
            log_callback("⚠️ 所有来源提取策略都失败，使用默认值: 本站")
    elif log_callback:
        log_callback(f"✓ 最终提取的来源: {source}")

    return source

# ==================== 异步版本的保存文章函数 ====================

async def save_article_async(
    link, save_dir, page_title, content_selectors,
    date_selectors=None, source_selectors=None, title_selectors=None,
    content_type="CSS", collect_links=True, mode="balance", filters=None,
    export_filename=None,  # 新增参数
    classid="",            # 新增参数，允许自定义classid
    file_format="CSV",     # 新增参数，文件格式
    excel_write_strategy="smart",  # 新增参数，Excel写入策略
    retry=2,                # 新增参数，重试次数
    interval=0,             # 新增参数，下载间隔（秒）
    log_callback=None,      # 新增参数，日志回调
    use_module_config=True, # 新增参数，是否使用模组配置
    custom_fields=None,     # 新增参数，自定义字段配置
    extended_fields=None,   # 新增参数，扩展字段列表
    # 字段配置参数
    field_preset=None,      # 新增参数，字段预设名称
    custom_field_list=None, # 新增参数，自定义字段列表
    user_custom_fields=None, # 新增参数，用户自定义字段字典（字段名:值）
    use_field_config=False, # 新增参数，是否使用字段配置
    # 浏览器配置参数
    headless=True,          # 新增参数，是否无头模式
    disable_js_injection=False  # 新增参数，是否禁用JS注入
):
    """
    异步版本的保存文章函数，优先使用Playwright安全模式
    支持多选择器：title_selectors, date_selectors, source_selectors
    支持模组配置：根据URL自动选择合适的配置
    """
    # 尝试使用模组配置
    if use_module_config and USE_MODULE_MANAGER:
        try:
            module_config = get_config_for_url(link)
            if module_config:
                module_name = match_module_for_url(link)
                logger.info(f"使用模组配置: {module_name} 处理URL: {link}")

                # 只有模组配置中有相同字段时才覆盖，否则使用基础配置（传入参数）
                if 'title_selectors' in module_config:
                    title_selectors = module_config['title_selectors']
                if 'date_selectors' in module_config:
                    date_selectors = module_config['date_selectors']
                if 'source_selectors' in module_config:
                    source_selectors = module_config['source_selectors']
                if 'content_selectors' in module_config:
                    content_selectors = module_config['content_selectors']

                if 'content_type' in module_config:
                    content_type = module_config['content_type']

                # 更新其他设置
                if 'mode' in module_config:
                    mode = module_config['mode']
                if 'collect_links' in module_config:
                    collect_links = module_config['collect_links']
                if 'retry' in module_config:
                    retry = module_config['retry']
                if 'interval' in module_config:
                    interval = module_config['interval']
        except Exception as e:
            logger.warning(f"使用模组配置失败，回退到传统配置: {e}")

    # 处理字段配置 - 参考模组配置的逻辑
    if use_field_config:
        try:
            from .field_config_manager import get_field_config_manager

            manager = get_field_config_manager()
            field_configs = {}

            if field_preset:
                # 获取预设字段配置
                presets = manager.get_field_presets()
                if field_preset in presets:
                    field_names = presets[field_preset]
                    all_fields = manager.get_available_fields()
                    for field_name in field_names:
                        if field_name in all_fields:
                            field_configs[field_name] = all_fields[field_name]

            elif custom_field_list:
                # 获取自定义字段配置
                all_fields = manager.get_available_fields()
                for field_name in custom_field_list:
                    if field_name in all_fields:
                        field_configs[field_name] = all_fields[field_name]

            # 直接覆盖选择器变量 - 参考模组配置的做法
            if field_configs:
                # 只有字段配置中有相同字段时才覆盖，否则使用基础配置（传入参数）
                if 'title' in field_configs and 'selectors' in field_configs['title']:
                    title_selectors = field_configs['title']['selectors']

                if 'content' in field_configs and 'selectors' in field_configs['content']:
                    content_selectors = field_configs['content']['selectors']

                if 'dateget' in field_configs and 'selectors' in field_configs['dateget']:
                    date_selectors = field_configs['dateget']['selectors']

                if 'source' in field_configs and 'selectors' in field_configs['source']:
                    source_selectors = field_configs['source']['selectors']

        except ImportError:
            pass  # 字段配置功能不可用，使用默认字段
        except Exception as e:
            logger.warning(f"应用字段配置失败: {e}")

    # 确保选择器为列表格式
    if title_selectors is None:
        title_selectors = []
    elif isinstance(title_selectors, str):
        title_selectors = [title_selectors]

    if date_selectors is None:
        date_selectors = []
    elif isinstance(date_selectors, str):
        date_selectors = [date_selectors]

    if source_selectors is None:
        source_selectors = []
    elif isinstance(source_selectors, str):
        source_selectors = [source_selectors]



    # 判断文件名和格式
    if export_filename:
        if file_format.upper() == "EXCEL":
            filename = f"{export_filename}.xlsx"
        else:
            filename = f"{export_filename}.csv"
        file_path = os.path.join(save_dir, filename)
    else:
        if file_format.upper() == "EXCEL":
            file_path = os.path.join(save_dir, f"{page_title}.xlsx")
        else:
            file_path = os.path.join(save_dir, f"{page_title}.csv")

    # 设置city字段 - 只使用文件名，不包含路径
    city = ""
    if export_filename:
        # 提取文件名（去除路径）
        filename_only = os.path.basename(export_filename)
        # 去除扩展名
        filename_without_ext = os.path.splitext(filename_only)[0]

        if "_" in filename_without_ext:
            city = filename_without_ext.split("_")[0]
        else:
            city = filename_without_ext

    # --- 异步Playwright安全模式 ---
    async def fetch_by_playwright_async():
        """使用异步Playwright进行安全模式的内容提取"""
        for attempt in range(retry+1):
            try:
                async with async_playwright() as p:
                    browser, context, page = await launch_browser(
                        p,
                        headless=headless,  # 使用传入的headless参数
                        browser_type="chromium",
                        disable_js_injection=disable_js_injection  # 使用传入的JS注入控制参数
                    )

                    try:
                        # 使用健壮等待策略访问文章页面
                        logger.info(f"使用健壮等待策略访问文章: {link}")
                        success = await robust_goto(page, link)

                        if not success:
                            logger.error(f"文章页面访问失败: {link}")
                            # 保存失败URL
                            await save_failed_url_async(link, "页面访问失败", save_dir, export_filename, article_title, file_format)
                            return False

                        logger.info(f"✅ 文章页面访问成功: {link}")

                        # 提取内容 - 改进逻辑：收集所有选择器的结果
                        content_text = ""
                        content = None
                        all_content_results = []  # 收集所有选择器的内容

                        for selector in content_selectors:
                            try:
                                # 对于某些选择器（如p, div, span等），尝试获取所有匹配的元素
                                if selector.lower() in ['p', 'div', 'span', 'li', 'td']:
                                    content_elems = await page.query_selector_all(selector)
                                    if content_elems:
                                        # 合并所有匹配元素的内容
                                        combined_html = ""
                                        for elem in content_elems:
                                            try:
                                                elem_html = await elem.inner_html()
                                                if elem_html and elem_html.strip():
                                                    combined_html += elem_html + "\n"
                                            except:
                                                continue

                                        if combined_html:
                                            # 在提取文本之前清理HTML
                                            from utils.text_cleaner import clean_html_before_text_extraction
                                            cleaned_html = clean_html_before_text_extraction(combined_html)

                                            # 使用清理后的HTML创建BeautifulSoup对象并提取文本
                                            temp_soup = BeautifulSoup(cleaned_html, "html.parser")
                                            selector_content = temp_soup.get_text(separator='\n', strip=True)

                                            logger.info(f"选择器 {selector} 找到 {len(content_elems)} 个元素，合并内容长度: {len(selector_content) if selector_content else 0}")

                                            # 收集所有选择器的内容
                                            if selector_content and selector_content.strip():
                                                all_content_results.append(selector_content.strip())

                                                # 如果这是第一个有效内容，设置为主要内容
                                                if not content_text:
                                                    content_text = selector_content
                                                    content = BeautifulSoup(cleaned_html, "html.parser")
                                                    logger.info(f"使用选择器 {selector} 的内容作为主要内容: {content_text[:100]}...")
                                            else:
                                                logger.warning(f"选择器 {selector} 找到 {len(content_elems)} 个元素但合并内容为空")
                                        else:
                                            logger.warning(f"选择器 {selector} 找到 {len(content_elems)} 个元素但所有元素内容都为空")
                                    else:
                                        logger.warning(f"选择器 {selector} 未找到元素")
                                else:
                                    # 对于其他选择器，使用原来的单元素逻辑
                                    content_elem = await page.query_selector(selector)

                                    if content_elem:
                                        # 先获取HTML内容
                                        content_html = await content_elem.inner_html()

                                        # 在提取文本之前清理HTML（移除style、script等标签）
                                        from utils.text_cleaner import clean_html_before_text_extraction
                                        cleaned_html = clean_html_before_text_extraction(content_html)

                                        # 使用清理后的HTML创建BeautifulSoup对象并提取文本
                                        temp_soup = BeautifulSoup(cleaned_html, "html.parser")
                                        selector_content = temp_soup.get_text(separator='\n', strip=True)

                                        logger.info(f"选择器 {selector} 找到内容，长度: {len(selector_content) if selector_content else 0}")

                                        # 收集所有选择器的内容
                                        if selector_content and selector_content.strip():
                                            all_content_results.append(selector_content.strip())

                                            # 如果这是第一个有效内容，设置为主要内容
                                            if not content_text:
                                                content_text = selector_content
                                                content = BeautifulSoup(cleaned_html, "html.parser")
                                                logger.info(f"使用选择器 {selector} 的内容作为主要内容: {content_text[:100]}...")
                                        else:
                                            logger.warning(f"选择器 {selector} 找到元素但内容为空")
                                    else:
                                        logger.warning(f"选择器 {selector} 未找到元素")
                            except Exception as e:
                                logger.warning(f"选择器 {selector} 处理失败: {e}")
                                continue

                        # 标题提取
                        page_title_val = ""
                        if title_selectors:
                            for title_sel in title_selectors:
                                if title_sel:
                                    try:
                                        title_elem = await page.query_selector(title_sel)

                                        if title_elem:
                                            page_title_val = await title_elem.text_content()
                                            if page_title_val and page_title_val.strip():
                                                page_title_val = page_title_val.strip()
                                                break
                                    except Exception:
                                        continue

                        page_title_val = text_cleaner.clean_title(page_title_val)

                        # 处理内容
                        if content_text:
                            text = text_cleaner.clean_html_tags(content_text)
                            imgs, attaches = [], []

                            # 提取图片和附件链接
                            if collect_links and content:
                                imgs = [img['src'] for img in content.find_all("img") if img.get("src")]
                                for a in content.find_all("a", href=True):
                                    href = a['href']
                                    # 确保href是字符串类型
                                    if isinstance(href, bytes):
                                        href = href.decode('utf-8', errors='ignore')
                                    href = str(href)
                                    if any(href.lower().endswith(ext) for ext in [".pdf", ".doc", ".docx", ".zip", ".rar", ".xls", ".xlsx"]):
                                        attaches.append(href)

                            # 增强的日期提取（支持微信公众号等特殊格式）
                            date = ""
                            if date_selectors:
                                for date_sel in date_selectors:
                                    if date_sel and not date:  # 如果已找到日期就跳出
                                        try:
                                            date_elem = await page.query_selector(date_sel)

                                            if date_elem:
                                                date_text = await date_elem.text_content()
                                                if date_text:
                                                    date_text = date_text.strip()
                                                    # 处理各种日期格式
                                                    if "日期：" in date_text:
                                                        date = date_text.split("日期：")[-1].strip().split()[0]
                                                    elif "发布时间：" in date_text:
                                                        date = date_text.replace("发布时间：", "").strip()
                                                    elif "时间：" in date_text:
                                                        date = date_text.replace("时间：", "").strip()
                                                    else:
                                                        date = date_text

                                                    date = text_cleaner.normalize_date(date)
                                                    if date:
                                                        break
                                        except Exception as e:
                                            if log_callback:
                                                log_callback(f"日期选择器 {date_sel} 提取失败: {e}")
                                            continue

                            # 如果仍然没有找到日期，尝试从页面脚本中提取（针对微信公众号）
                            if not date:
                                try:
                                    script_content = await page.content()
                                    import re

                                    # 查找常见的时间变量模式
                                    time_patterns = [
                                        r'publish_time["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
                                        r'createTime["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
                                        r'pubDate["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
                                        r'date["\']?\s*[:=]\s*["\']?(\d{4}[-/]\d{1,2}[-/]\d{1,2}[^"\']*)',
                                    ]

                                    for pattern in time_patterns:
                                        match = re.search(pattern, script_content, re.IGNORECASE)
                                        if match:
                                            date = match.group(1).strip()
                                            date = text_cleaner.normalize_date(date)
                                            if log_callback:
                                                log_callback(f"从脚本中提取到日期: {date}")
                                            break
                                except Exception as e:
                                    if log_callback:
                                        log_callback(f"从脚本提取日期失败: {e}")

                            # 调试信息
                            if log_callback:
                                if date:
                                    log_callback(f"成功提取日期: {date}")
                                else:
                                    log_callback("⚠️ 未能提取到日期信息")

                            # 健壮的来源提取逻辑
                            source = ""
                            if source_selectors:
                                for source_sel in source_selectors:
                                    if source_sel:
                                        try:
                                            source_elems = await page.query_selector_all(source_sel)

                                            # 尝试所有匹配的元素，而不只是第一个或第二个
                                            for i, elem in enumerate(source_elems):
                                                try:
                                                    source_text = await elem.text_content()
                                                    if source_text:
                                                        source_text = source_text.strip()

                                                        # 使用增强的来源提取函数
                                                        extracted_source = text_cleaner.extract_source_from_text(source_text)

                                                        if extracted_source and extracted_source != "本站" and len(extracted_source) > 1:
                                                            source = text_cleaner.normalize_source(extracted_source)
                                                            if source:  # 确保normalize_source没有返回空字符串
                                                                if log_callback:
                                                                    log_callback(f"✓ 选择器 {source_sel}[{i}] 提取到来源: {source}")
                                                                break

                                                except Exception as e:
                                                    if log_callback:
                                                        log_callback(f"⚠️ 处理选择器 {source_sel}[{i}] 时出错: {e}")
                                                    continue

                                            # 如果找到有效来源，停止尝试其他选择器
                                            if source and source != "本站":
                                                break

                                        except Exception as e:
                                            if log_callback:
                                                log_callback(f"⚠️ 选择器 {source_sel} 执行失败: {e}")
                                            continue

                            # 如果选择器提取失败，尝试从页面HTML中智能提取
                            if not source or source == "本站":
                                try:
                                    # 尝试从特定的常见位置提取来源
                                    common_selectors = [
                                        '.article_date',
                                        '.source',
                                        '.info-source',
                                        '.article-source',
                                        '.article-info',
                                        '.meta-source',
                                        '[class*="source"]',
                                        '[class*="来源"]',
                                        'script[type="text/javascript"]'
                                    ]

                                    for selector in common_selectors:
                                        try:
                                            elems = await page.query_selector_all(selector)
                                            for elem in elems:
                                                try:
                                                    if selector.startswith('script'):
                                                        # 对于script标签，获取innerHTML
                                                        content = await elem.inner_html()
                                                    else:
                                                        content = await elem.text_content()

                                                    if content:
                                                        extracted_source = text_cleaner.extract_source_from_text(content)
                                                        if extracted_source and extracted_source != "本站" and len(extracted_source) > 1:
                                                            source = text_cleaner.normalize_source(extracted_source)
                                                            if source:  # 确保normalize_source没有返回空字符串
                                                                if log_callback:
                                                                    log_callback(f"✓ 通用选择器 {selector} 提取到来源: {source}")
                                                                break

                                                except Exception:
                                                    continue

                                            if source and source != "本站":
                                                break

                                        except Exception:
                                            continue

                                except Exception as e:
                                    if log_callback:
                                        log_callback(f"⚠️ 智能来源提取失败: {e}")

                            # 最终处理
                            if not source:
                                source = "本站"
                                if log_callback:
                                    log_callback("⚠️ 所有来源提取策略都失败，使用默认值: 本站")
                            elif log_callback:
                                log_callback(f"✓ 最终提取的来源: {source}")

                            return text, date, source, imgs, attaches, page_title_val, all_content_results
                        else:
                            return None, "", "", [], [], page_title_val, []

                    finally:
                        await context.close()
                        await browser.close()

            except Exception as e:
                logger.warning(f"异步Playwright第{attempt+1}次尝试失败: {e}")
                if attempt < retry:
                    await asyncio.sleep(1)

        return None, "", "", [], [], "", []

    # 使用异步Playwright模式
    try:
        content_text, article_date, article_source, img_links, attach_links, article_title, all_content_results = await fetch_by_playwright_async()
    except Exception as e:
        logger.error(f"异步Playwright模式失败: {e}")
        return False

    # 后续处理逻辑与同步版本相同
    # 正文过滤：使用增强型过滤（集成传统过滤和非文本字段清洗）
    try:
        from utils.text_cleaner import enhanced_content_filter
        content_text = enhanced_content_filter(content_text, filters)
    except ImportError:
        # 回退到传统过滤
        logger.warning("无法导入增强型过滤功能，使用传统过滤")
        content_text = text_cleaner.filter_content(content_text, filters)
    all_links = []
    if collect_links:
        if img_links:
            all_links.append("图片链接: " + ", ".join(img_links))
        if attach_links:
            all_links.append("附件链接: " + ", ".join(attach_links))
        if all_links and content_text:
            content_text += "[" + " | ".join(all_links) + "]"

    if not article_source:
        article_source = "本站"

    # 检查内容是否为空 - 改进逻辑：检查所有选择器的集合结果
    if is_all_selectors_content_empty(all_content_results):
        logger.warning(f"所有选择器提取的内容都为空，记录为失败: {article_title} - {link}")
        logger.info(f"尝试的选择器数量: {len(content_selectors)}, 有效内容数量: {len(all_content_results)}")
        # 异步保存失败URL
        await save_failed_url_async(link, "所有选择器提取的内容都为空", save_dir, export_filename, article_title, file_format)
        return False

    # 使用灵活字段提取器
    try:
        field_extractor = get_field_extractor()

        # 配置自定义字段（如果提供）
        if custom_fields:
            configure_fields(custom_fields)

        # 添加扩展字段（如果提供）
        if extended_fields:
            add_extended_fields(extended_fields)

        # 准备静态值
        static_values = {
            'articlelink': link,
            'content': content_text,
            'classid': classid,
            'city': city,
            'title': article_title,
            'dateget': article_date,
            'source': article_source
        }

        # 添加用户自定义字段
        if user_custom_fields:
            for field_name, field_value in user_custom_fields.items():
                static_values[field_name] = field_value

        # 提取所有字段值
        data_row = await field_extractor.extract_fields(
            page=None,  # 如果需要页面对象，需要传入
            url=link,
            content_html=content_text,  # 这里传入的是处理后的内容
            static_values=static_values
        )

        # 获取表头
        headers = field_extractor.get_headers()

    except Exception as e:
        logger.warning(f"使用字段提取器失败，回退到默认字段: {e}")
        # 回退到原始字段结构
        now_str = time.strftime('%Y-%m-%d %H:%M:%S')
        data_row = [
            article_date, article_source, article_title, link, content_text, classid, city, now_str
        ]
        headers = ['dateget', 'source', 'title', 'articlelink', 'content', 'classid', 'city', 'getdate']

    # 使用统一的异步数据写入函数
    success = await write_article_data_async(file_path, data_row, headers, file_format, excel_write_strategy)
    if success:
        logger.info(f"已保存: {article_title}")
        return True
    else:
        # 异步保存失败URL
        await save_failed_url_async(link, "数据写入失败", save_dir, export_filename, article_title, file_format)
        return False

# ==================== 文章批处理函数 ====================

async def process_articles_batch(all_articles,
                          content_selectors,
                          title_selectors=None,
                          date_selectors=None,
                          source_selectors=None,
                          content_type="CSS",
                          collect_links=True,
                          mode="balance",
                          filters=None,
                          export_filename=None,
                          classid="",
                          file_format="CSV",
                          excel_write_strategy="smart",
                          retry=2,
                          interval=0,
                          max_workers=5,
                          log_callback=None,
                          progress_callback=None,
                          use_module_config=True,
                          # 字段配置参数
                          field_preset=None,
                          custom_field_list=None,
                          user_custom_fields=None,
                          use_field_config=False,
                          # 浏览器配置参数
                          headless=True,
                          disable_js_injection=False):
    """
    批量处理文章列表（下载和保存）
    支持多选择器：title_selectors, date_selectors, source_selectors

    Args:
        all_articles: 文章信息列表，格式为 [(title, href, save_dir, page_title, page_url, classid), ...]
        其他参数: 处理配置参数

    Returns:
        dict: 处理结果统计
    """
    # 确保选择器为列表格式
    if title_selectors is None:
        title_selectors = []
    elif isinstance(title_selectors, str):
        title_selectors = [title_selectors]

    if date_selectors is None:
        date_selectors = []
    elif isinstance(date_selectors, str):
        date_selectors = [date_selectors]

    if source_selectors is None:
        source_selectors = []
    elif isinstance(source_selectors, str):
        source_selectors = [source_selectors]

    if content_selectors is None:
        content_selectors = []
    elif isinstance(content_selectors, str):
        content_selectors = [content_selectors]

    # 处理字段配置 - 参考模组配置的逻辑
    if use_field_config:
        try:
            from .field_config_manager import get_field_config_manager

            manager = get_field_config_manager()
            field_configs = {}

            if field_preset:
                if log_callback:
                    log_callback(f"🔧 批处理应用字段预设: {field_preset}")
                # 获取预设字段配置
                presets = manager.get_field_presets()
                if field_preset in presets:
                    field_names = presets[field_preset]
                    all_fields = manager.get_available_fields()
                    for field_name in field_names:
                        if field_name in all_fields:
                            field_configs[field_name] = all_fields[field_name]

            elif custom_field_list:
                if log_callback:
                    log_callback(f"🔧 批处理应用自定义字段: {len(custom_field_list)} 个字段")
                # 获取自定义字段配置
                all_fields = manager.get_available_fields()
                for field_name in custom_field_list:
                    if field_name in all_fields:
                        field_configs[field_name] = all_fields[field_name]

            # 直接覆盖选择器变量 - 参考模组配置的做法
            if field_configs:
                if log_callback:
                    log_callback(f"🔧 批处理字段配置覆盖选择器: {len(field_configs)} 个字段")

                # 只有字段配置中有相同字段时才覆盖，否则使用基础配置（传入参数）
                if 'title' in field_configs and 'selectors' in field_configs['title']:
                    title_selectors = field_configs['title']['selectors']
                    if log_callback:
                        log_callback(f"   📝 批处理 title: {len(title_selectors)} 个选择器")

                if 'content' in field_configs and 'selectors' in field_configs['content']:
                    content_selectors = field_configs['content']['selectors']
                    if log_callback:
                        log_callback(f"   📝 批处理 content: {len(content_selectors)} 个选择器")

                if 'dateget' in field_configs and 'selectors' in field_configs['dateget']:
                    date_selectors = field_configs['dateget']['selectors']
                    if log_callback:
                        log_callback(f"   📝 批处理 dateget: {len(date_selectors)} 个选择器")

                if 'source' in field_configs and 'selectors' in field_configs['source']:
                    source_selectors = field_configs['source']['selectors']
                    if log_callback:
                        log_callback(f"   📝 批处理 source: {len(source_selectors)} 个选择器")

        except ImportError:
            if log_callback:
                log_callback("⚠️ 批处理字段配置功能不可用，使用默认字段")
        except Exception as e:
            if log_callback:
                log_callback(f"⚠️ 批处理应用字段配置失败: {e}")

    if not all_articles:
        return {
            'total_articles': 0,
            'processed_articles': 0,
            'failed_articles': 0,
            'save_dir': None
        }

    # 确定保存目录
    if export_filename:
        save_dir = create_save_dir("articles", export_filename=export_filename)
    else:
        # 使用第一篇文章的页面标题作为目录名
        save_dir = create_save_dir(all_articles[0][3] if all_articles else "articles")

    success_count = 0
    fail_count = 0
    total_to_process = len(all_articles)



    if log_callback:
        log_callback(f"开始批量处理 {total_to_process} 篇文章...")
    else:
        logger.info(f"开始批量处理 {total_to_process} 篇文章...")

    # 异步并发处理（使用信号量控制并发数）
    # 创建信号量控制并发数
    semaphore = asyncio.Semaphore(max_workers)

    async def save_one_async(args):
        """异步版本的单文章处理函数"""
        async with semaphore:  # 控制并发数
            title, href, article_save_dir, page_title, page_url, classid_val = args
            # 如果有全局导出文件名，使用统一的保存目录
            final_save_dir = save_dir if export_filename else article_save_dir

            try:
                # 为每个URL单独应用模组配置
                article_content_selectors = content_selectors
                article_title_selectors = title_selectors
                article_date_selectors = date_selectors
                article_source_selectors = source_selectors
                article_content_type = content_type

                article_mode = mode
                article_collect_links = collect_links
                article_retry = retry
                article_interval = interval

                # 如果启用模组配置，为当前URL获取特定配置
                if use_module_config and USE_MODULE_MANAGER:
                    try:
                        module_config = get_config_for_url(href)
                        if module_config:
                            module_name = match_module_for_url(href)
                            if log_callback:
                                log_callback(f"文章使用模组配置: {module_name} 处理URL: {href}")
                            else:
                                logger.info(f"文章使用模组配置: {module_name} 处理URL: {href}")

                            # 应用模组配置覆盖默认配置
                            if 'title_selectors' in module_config:
                                article_title_selectors = module_config['title_selectors']
                            if 'date_selectors' in module_config:
                                article_date_selectors = module_config['date_selectors']
                            if 'source_selectors' in module_config:
                                article_source_selectors = module_config['source_selectors']
                            if 'content_selectors' in module_config:
                                article_content_selectors = module_config['content_selectors']


                            if 'content_type' in module_config:
                                article_content_type = module_config['content_type']

                            # 更新其他设置
                            if 'mode' in module_config:
                                article_mode = module_config['mode']
                            if 'collect_links' in module_config:
                                article_collect_links = module_config['collect_links']
                            if 'retry' in module_config:
                                article_retry = module_config['retry']
                            if 'interval' in module_config:
                                article_interval = module_config['interval']
                    except Exception as e:
                        if log_callback:
                            log_callback(f"应用模组配置失败，使用默认配置: {e}")
                        else:
                            logger.warning(f"应用模组配置失败，使用默认配置: {e}")

                result = await save_article_async(
                    href, final_save_dir, page_title, article_content_selectors,
                    date_selectors=article_date_selectors, source_selectors=article_source_selectors, title_selectors=article_title_selectors,
                    content_type=article_content_type, collect_links=article_collect_links, mode=article_mode, filters=filters,
                    export_filename=export_filename, classid=classid_val, file_format=file_format,
                    excel_write_strategy=excel_write_strategy,
                    retry=article_retry, interval=article_interval, use_module_config=False,  # 已经在这里应用了配置，避免重复应用
                    # 传递字段配置参数
                    field_preset=field_preset, custom_field_list=custom_field_list, user_custom_fields=user_custom_fields, use_field_config=False,  # 已经在这里应用了配置，避免重复应用
                    # 传递浏览器配置参数
                    headless=headless, disable_js_injection=disable_js_injection
                )
                return result
            except Exception as e:
                if log_callback:
                    log_callback(f"处理文章时出错: {e}")
                else:
                    logger.error(f"处理文章时出错: {e}")
                return False

    # 创建所有任务
    tasks = [save_one_async(article) for article in all_articles]

    # 使用 asyncio.as_completed 处理任务完成
    for coro in asyncio.as_completed(tasks):
        try:
            result = await coro
            if result:
                success_count += 1
            else:
                fail_count += 1
        except Exception as e:
            fail_count += 1
            if log_callback:
                log_callback(f"处理文章时出错: {e}")
            else:
                logger.error(f"处理文章时出错: {e}")

        # 进度报告
        processed = success_count + fail_count
        if log_callback:
            log_callback(f"进度: {processed}/{total_to_process} (成功: {success_count}, 失败: {fail_count})")
        else:
            logger.info(f"进度: {processed}/{total_to_process} (成功: {success_count}, 失败: {fail_count})")

        # 新增：调用 progress_callback 实时更新进度条
        if progress_callback:
            try:
                progress_callback(processed, total_to_process)
            except Exception as e:
                if log_callback:
                    log_callback(f"进度回调异常: {e}")
                else:
                    logger.warning(f"进度回调异常: {e}")

    if log_callback:
        log_callback(f"批量处理完成！成功: {success_count}, 失败: {fail_count}")
    else:
        logger.info(f"批量处理完成！成功: {success_count}, 失败: {fail_count}")

    # 刷新Excel缓存，确保所有数据都写入文件
    if file_format.upper() == "EXCEL":
        if log_callback:
            log_callback("正在刷新Excel缓存...")
        else:
            logger.info("正在刷新Excel缓存...")

        if EXCEL_WRITER_AVAILABLE:
            # 使用新的ExcelWriter类
            excel_writer = get_excel_writer()
            flush_success = excel_writer.flush_cache()
            converted_count = excel_writer.finalize_hybrid_files(save_dir)
        else:
            # Excel写入器不可用，无法执行清理操作
            logger.warning("Excel写入器不可用，无法执行Excel缓存清理")
            flush_success = False
            converted_count = 0

        if flush_success:
            if log_callback:
                log_callback("Excel缓存刷新完成")
            else:
                logger.info("Excel缓存刷新完成")
        else:
            if log_callback:
                log_callback("Excel缓存刷新失败")
            else:
                logger.warning("Excel缓存刷新失败")

        # 完成混合策略文件的最终转换
        if converted_count > 0:
            if log_callback:
                log_callback(f"完成{converted_count}个混合文件的最终转换")
            else:
                logger.info(f"完成{converted_count}个混合文件的最终转换")

    # 构建失败URL文件路径
    failed_file_path = None
    if fail_count > 0:
        if export_filename:
            if file_format.upper() == "EXCEL":
                failed_filename = f"{export_filename}_failed.xlsx"
            else:
                failed_filename = f"{export_filename}_failed.csv"
            failed_file_path = os.path.join(save_dir, failed_filename)
        else:
            if file_format.upper() == "EXCEL":
                failed_file_path = os.path.join(save_dir, "failed_urls.xlsx")
            else:
                failed_file_path = os.path.join(save_dir, "failed_urls.csv")

    return {
        'total': len(all_articles),
        'success': success_count,
        'failed': fail_count,
        'total_articles': len(all_articles),
        'processed_articles': success_count,
        'failed_articles': fail_count,
        'save_dir': save_dir if success_count > 0 else None,
        'failed_file': failed_file_path,
        'failed_reasons': {
            '文章内容为空': fail_count,  # 简化统计，实际可以更详细
        } if fail_count > 0 else {}
    }

# ==================== 传统分页处理函数 ====================

async def crawl_traditional_pagination_playwright(page: Page, input_url, base_url, max_pages=None,
                               list_container_selector=".main",
                               article_item_selector=".clearfix.ty_list li a",
                               title_selectors=None,
                               page_suffix="index_{n}.html",
                               url_mode="absolute",
                               log_callback=None,
                               classid="",
                               stop_check_callback=None,
                               start_page=1):
    """
    传统分页爬取功能 (Playwright版本)

    Args:
        page: Playwright Page实例
        input_url: 起始URL
        base_url: 基础URL
        max_pages: 最大页数
        其他参数: 选择器和配置参数

    Returns:
        tuple: (all_articles, found_urls, total_articles)
    """
    processed_titles = set()
    all_articles = []
    found_urls = set()
    page_num = start_page  # 从指定的起始页开始
    total_articles = 0

    # 处理max_pages为0或None的情况
    if max_pages is not None:
        try:
            max_pages = int(max_pages)
            if max_pages <= 0:
                max_pages = None
        except ValueError:
            max_pages = None

    while True:
        # 检查是否请求停止
        if stop_check_callback and stop_check_callback():
            msg = "用户请求停止爬取"
            if log_callback:
                log_callback(msg)
            else:
                logger.info(msg)
            break

        if max_pages is not None and page_num > max_pages:
            msg = f"已达到最大页数限制: {max_pages}"
            if log_callback:
                log_callback(msg)
            else:
                logger.info(msg)
            break

        # 生成页面URL
        if page_num == start_page:
            page_url = input_url
        else:
            # 使用input_url作为基础URL进行翻页
            page_url = get_page_url(
                input_url=input_url,
                page_num=page_num,
                page_suffix=page_suffix,
                base_url=base_url,
                start_page=start_page
            )

        if log_callback:
            log_callback(f"[DEBUG] page_num={page_num}, page_url={page_url}")
        else:
            logger.info(f"[DEBUG] page_num={page_num}, page_url={page_url}")

        try:
            articles, page_title, article_links, article_titles = await get_article_links_playwright(
                page, page_url, list_container_selector, article_item_selector, title_selectors)
            total_articles += len(articles)

        except Exception as e:
            msg = f"获取文章链接时出错: {str(e)}"
            # 遇到404直接跳过该页
            if "404" in str(e) or "Not Found" in str(e):
                msg2 = f"页面404，跳过本页: {page_url}"
                if log_callback:
                    log_callback(msg2)
                else:
                    logger.info(msg2)
                page_num += 1
                break

            if log_callback:
                log_callback(msg)
            else:
                logger.error(msg)

            if "无法访问目标URL" in str(e):
                msg2 = f"请检查网络连接或目标网站是否可访问: {page_url}"
                if log_callback:
                    log_callback(msg2)
                else:
                    logger.error(msg2)
            elif "未找到列表容器" in str(e) or "未找到文章项" in str(e):
                msg2 = f"页面结构可能已变动，请检查选择器设置: {list_container_selector} / {article_item_selector}"
                if log_callback:
                    log_callback(msg2)
                else:
                    logger.error(msg2)
                break
            else:
                break

        if not articles:
            msg = "没有更多文章，采集结束。"
            if log_callback:
                log_callback(msg)
            else:
                logger.info(msg)
            break

        # 处理文章信息
        save_dir = create_save_dir(page_title)
        new_found = False

        for href, title in zip(article_links, article_titles):
            if not href:
                continue
            full_url = get_full_link(href, page_url, base_url, url_mode)
            found_urls.add(full_url)
            if title in processed_titles:
                continue
            # 修复：保存完整URL而不是原始href
            all_articles.append((title, full_url, save_dir, page_title, page_url, classid))
            new_found = True
            processed_titles.add(title)

        if not new_found:
            msg = "没有新文章。"
            if log_callback:
                log_callback(msg)
            else:
                logger.info(msg)
            break

        page_num += 1

    if log_callback:
        log_callback(f"共找到 {total_articles} 篇文章")
    else:
        logger.info(f"共找到 {total_articles} 篇文章")

    return all_articles, found_urls, total_articles

# ==================== URL去重处理函数 ====================

def deduplicate_articles_by_url(articles, log_callback=None):
    """
    根据URL对文章列表进行去重处理

    Args:
        articles: 文章列表，每个文章应该包含'url'或'href'字段
        log_callback: 日志回调函数

    Returns:
        去重后的文章列表
    """
    if not articles:
        return articles

    seen_urls = set()
    deduplicated_articles = []
    duplicate_count = 0

    for article in articles:
        # 获取文章URL，支持多种数据格式
        url = None

        # 处理元组格式：(title, url, save_dir, page_title, page_url, classid)
        if isinstance(article, tuple) and len(article) >= 2:
            url = article[1]  # URL在第二个位置
        # 处理字典格式
        elif isinstance(article, dict):
            url = article.get('url') or article.get('href') or article.get('link')
        # 处理对象格式
        elif hasattr(article, 'url'):
            url = article.url
        elif hasattr(article, 'href'):
            url = article.href
        elif hasattr(article, 'link'):
            url = article.link

        if not url:
            # 如果没有URL，保留文章但记录警告
            if log_callback:
                log_callback(f"⚠️ 发现无URL的文章，保留: {article}")
            else:
                logger.warning(f"发现无URL的文章，保留: {article}")
            deduplicated_articles.append(article)
            continue

        # 规范化URL（去除片段标识符和常见参数）
        normalized_url = normalize_url_for_deduplication(url)

        if normalized_url not in seen_urls:
            seen_urls.add(normalized_url)
            deduplicated_articles.append(article)
        else:
            duplicate_count += 1
            if log_callback:
                log_callback(f"🔄 发现重复URL，跳过: {url}")
            else:
                logger.debug(f"发现重复URL，跳过: {url}")

    if log_callback:
        log_callback(f"📊 去重统计: 原始 {len(articles)} 篇 → 去重后 {len(deduplicated_articles)} 篇 (移除 {duplicate_count} 个重复)")
    else:
        logger.info(f"去重统计: 原始 {len(articles)} 篇 → 去重后 {len(deduplicated_articles)} 篇 (移除 {duplicate_count} 个重复)")

    return deduplicated_articles


# ==================== CSV URL缓存功能 ====================

def save_collected_urls_to_csv(urls, input_url, config_group=None, log_callback=None):
    """
    保存翻页收集的URL到CSV文件

    Args:
        urls: URL列表
        input_url: 列表页URL，用于生成文件名
        config_group: 配置组名称
        log_callback: 日志回调函数

    Returns:
        str: 保存的CSV文件路径，失败时返回None
    """
    if not urls or not input_url:
        return None

    try:
        # 从 input_url 生成安全的文件名
        from urllib.parse import urlparse
        parsed_url = urlparse(input_url)
        domain = parsed_url.netloc.replace('.', '_').replace(':', '_')
        path_part = parsed_url.path.replace('/', '_').replace('.', '_')

        # 基础文件名
        base_filename = f"{domain}{path_part}_collected_urls.csv"
        base_filename = re.sub(r'[<>:"/\\|?*]', '_', base_filename)
        base_filename = base_filename.replace('__', '_').strip('_')

        # 创建专有的CSV文件夹结构
        csv_base_dir = "url_cache"
        if config_group:
            # 使用配置组创建子文件夹
            output_dir = os.path.join(csv_base_dir, config_group)
        else:
            # 默认文件夹
            output_dir = os.path.join(csv_base_dir, "default")

        # 确保目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        filepath = os.path.join(output_dir, base_filename)

        # 保存到 CSV 文件
        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['url'])  # 只写url表头，兼容读取
            for url in urls:
                writer.writerow([url])

        if log_callback:
            log_callback(f"💾 已保存收集的URL到: {filepath} ({len(urls)} 个链接)")
        else:
            logger.info(f"已保存收集的URL到: {filepath} ({len(urls)} 个链接)")

        return filepath

    except Exception as e:
        if log_callback:
            log_callback(f"⚠️ 保存收集的URL失败: {e}")
        else:
            logger.warning(f"保存收集的URL失败: {e}")
        return None


def load_cached_urls_from_csv(input_url, config_group=None, log_callback=None):
    """
    从CSV文件加载缓存的URL

    Args:
        input_url: 列表页URL，用于查找对应的CSV文件
        config_group: 配置组名称
        log_callback: 日志回调函数

    Returns:
        list: URL列表，失败时返回空列表
    """
    if not input_url:
        return []

    try:
        # 生成对应的CSV文件路径
        from urllib.parse import urlparse
        parsed_url = urlparse(input_url)
        domain = parsed_url.netloc.replace('.', '_').replace(':', '_')
        path_part = parsed_url.path.replace('/', '_').replace('.', '_')

        base_filename = f"{domain}{path_part}_collected_urls.csv"
        base_filename = re.sub(r'[<>:"/\\|?*]', '_', base_filename)
        base_filename = base_filename.replace('__', '_').strip('_')

        csv_base_dir = "url_cache"
        if config_group:
            output_dir = os.path.join(csv_base_dir, config_group)
        else:
            output_dir = os.path.join(csv_base_dir, "default")

        filepath = os.path.join(output_dir, base_filename)

        if not os.path.exists(filepath):
            if log_callback:
                log_callback(f"📄 未找到缓存的URL文件: {filepath}")
            return []

        # 读取CSV文件
        urls = []
        with open(filepath, 'r', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            headers = next(reader, None)  # 读取表头

            if not headers or 'url' not in headers:
                if log_callback:
                    log_callback(f"❌ CSV文件格式错误，缺少url列: {filepath}")
                return []

            url_index = headers.index('url')

            for row in reader:
                if len(row) > url_index and row[url_index].strip():
                    urls.append(row[url_index].strip())

        if log_callback:
            log_callback(f"📄 从缓存加载了 {len(urls)} 个URL: {os.path.basename(filepath)}")
        else:
            logger.info(f"从缓存加载了 {len(urls)} 个URL: {os.path.basename(filepath)}")

        return urls

    except Exception as e:
        if log_callback:
            log_callback(f"❌ 加载缓存URL失败: {e}")
        else:
            logger.error(f"加载缓存URL失败: {e}")
        return []


def check_url_cache_exists(input_url, config_group=None):
    """
    检查是否存在对应的URL缓存文件

    Args:
        input_url: 列表页URL
        config_group: 配置组名称

    Returns:
        tuple: (是否存在, 文件路径, 文件信息字典)
    """
    if not input_url:
        return False, None, None

    try:
        # 生成对应的CSV文件路径
        from urllib.parse import urlparse
        parsed_url = urlparse(input_url)
        domain = parsed_url.netloc.replace('.', '_').replace(':', '_')
        path_part = parsed_url.path.replace('/', '_').replace('.', '_')

        base_filename = f"{domain}{path_part}_collected_urls.csv"
        base_filename = re.sub(r'[<>:"/\\|?*]', '_', base_filename)
        base_filename = base_filename.replace('__', '_').strip('_')

        csv_base_dir = "url_cache"
        if config_group:
            output_dir = os.path.join(csv_base_dir, config_group)
        else:
            output_dir = os.path.join(csv_base_dir, "default")

        filepath = os.path.join(output_dir, base_filename)

        if os.path.exists(filepath):
            # 获取文件信息
            import datetime
            file_info = {
                'filepath': filepath,
                'filename': base_filename,
                'config_group': config_group or 'default',
                'size': os.path.getsize(filepath),
                'modified': os.path.getmtime(filepath),
                'modified_str': datetime.datetime.fromtimestamp(os.path.getmtime(filepath)).strftime('%Y-%m-%d %H:%M:%S')
            }
            return True, filepath, file_info
        else:
            return False, filepath, None

    except Exception as e:
        logger.error(f"检查URL缓存失败: {e}")
        return False, None, None

# ==================== 传统翻页收集函数 ====================

async def collect_articles_from_pagination(
    input_url, base_url=None, max_pages=None,
    list_container_selector=".main",
    article_item_selector=".clearfix.ty_list li a",
    title_selectors=None,
    page_suffix="index_{n}.html",
    start_page=1,
    url_mode="absolute",
    browser_type="chromium",
    headless=True,  # 默认无头模式
    disable_js_injection=False,  # 是否禁用JS注入
    classid="",
    log_callback=None,
    stop_check_callback=None,
    config_group=None,
    use_module_config=True
):
    """
    专门负责从网页收集文章链接的函数（传统翻页）

    Args:
        input_url: 起始URL
        base_url: 基础URL
        max_pages: 最大页数
        其他参数: 翻页和收集配置

    Returns:
        tuple: (collected_articles, found_urls, total_articles)
    """
    if log_callback:
        log_callback(f"🔍 开始传统翻页收集: {input_url}")
    else:
        logger.info(f"开始传统翻页收集: {input_url}")

    # 尝试使用模组配置
    if use_module_config and USE_MODULE_MANAGER:
        try:
            module_config = get_config_for_url(input_url)
            if module_config:
                module_name = match_module_for_url(input_url)
                if log_callback:
                    log_callback(f"📦 翻页收集使用模组配置: {module_name}")

                # 应用模组配置
                if 'title_selectors' in module_config:
                    title_selectors = module_config['title_selectors']
                if 'list_container_selector' in module_config:
                    list_container_selector = module_config['list_container_selector']
                if 'article_item_selector' in module_config:
                    article_item_selector = module_config['article_item_selector']
                if 'page_suffix' in module_config:
                    page_suffix = module_config['page_suffix']
                if 'max_pages' in module_config:
                    max_pages = module_config['max_pages']

        except Exception as e:
            if log_callback:
                log_callback(f"⚠️ 翻页收集模组配置失败: {e}")

    # JSP网站应该使用动态翻页模式，传统翻页不处理JSP
    if JSP_HANDLER_AVAILABLE and jsp_handler.is_supported_url(input_url):
        if log_callback:
            log_callback(f"⚠️ 检测到JSP网站，建议使用动态翻页模式")
        else:
            logger.warning(f"检测到JSP网站，建议使用动态翻页模式: {input_url}")

    async with async_playwright() as p:
        browser, context, page = await launch_browser(
            p,
            headless=headless,
            browser_type=browser_type,
            disable_js_injection=disable_js_injection
        )

        try:
            # 使用传统分页爬取
            collected_articles, found_urls, total_articles = await crawl_traditional_pagination_playwright(
                page=page,
                input_url=input_url,
                base_url=base_url,
                max_pages=max_pages,
                list_container_selector=list_container_selector,
                article_item_selector=article_item_selector,
                title_selectors=title_selectors,
                page_suffix=page_suffix,
                url_mode=url_mode,
                log_callback=log_callback,
                classid=classid,
                stop_check_callback=stop_check_callback,
                start_page=start_page
            )

            # 保存收集的URL到缓存
            if collected_articles and input_url:
                collected_urls = []
                for article_info in collected_articles:
                    if len(article_info) > 1:
                        collected_urls.append(article_info[1])

                if collected_urls:
                    save_collected_urls_to_csv(collected_urls, input_url, config_group, log_callback)

            return collected_articles, found_urls, total_articles

        except Exception as e:
            if log_callback:
                log_callback(f"❌ 传统翻页收集失败: {e}")
            else:
                logger.error(f"传统翻页收集失败: {e}")
            raise
        finally:
            await context.close()
            await browser.close()

# ==================== 主要爬取函数（重构版） ====================

async def crawl_articles_async(
    all_articles=None,  # 预定义文章列表（主要参数）
    # 内容提取配置
    content_selectors=[
        ".article_cont",
        "div[class*='content']",
        "div[class*='article']",
        "div.view.TRS_UEDITOR.trs_paper_default.trs_web",
        ".TRS_Editor",
        "div.zhengwen"
    ],
    title_selectors=None,
    date_selectors=None,
    source_selectors=None,
    content_type="CSS",
    # 处理配置
    collect_links=True,
    mode="balance",
    filters=None,
    export_filename=None,
    classid="",
    file_format="CSV",
    excel_write_strategy="smart",
    max_workers=5,
    retry=2,
    interval=0,
    headless=True,  # 默认无头模式
    disable_js_injection=False,  # 是否禁用JS注入
    # 回调函数
    log_callback=None,
    progress_callback=None,
    stop_check_callback=None,
    # 配置选项
    use_module_config=True,
    config_group=None,
    # 字段配置参数
    field_preset=None,
    custom_field_list=None,
    user_custom_fields=None,
    use_field_config=False,
    # 兼容性参数（保留但不推荐使用）
    input_url=None,
    base_url=None,
    max_pages=None,
    list_container_selector=".main",
    article_item_selector=".clearfix.ty_list li a",
    page_suffix="index_{n}.html",
    start_page=1,
    url_mode="absolute",
    browser_type="chromium",
    skip_pagination_if_cached=False
):
    """
    主要爬取函数（重构版）- 专注于处理文章内容提取

    重要变更：
    1. 主要负责处理其他模块路由过来的 all_articles
    2. 如果 all_articles 为空，直接显示"all_articles获取失败"并返回
    3. 传统翻页收集功能已转移到 collect_articles_from_pagination 函数

    Args:
        all_articles: 预处理的文章列表（必需参数）
        content_selectors: 内容选择器列表
        title_selectors: 标题选择器列表
        date_selectors: 日期选择器列表
        source_selectors: 来源选择器列表
        headless: 是否无头模式（默认True）
        disable_js_injection: 是否禁用JS注入（避免影响网站JS加载）
        其他参数: 内容提取和处理配置

    Returns:
        dict: 包含爬取结果的字典
    """
    # 检查 all_articles 参数
    if all_articles is None:
        error_msg = "❌ all_articles获取失败 - 未提供文章列表"
        if log_callback:
            log_callback(error_msg)
        else:
            logger.error(error_msg)
        return {
            'total': 0,
            'success': 0,
            'failed': 0,
            'results': [],
            'error': 'all_articles获取失败'
        }

    if log_callback:
        log_callback(f"✅ 接收到文章列表，共 {len(all_articles)} 篇文章")
    else:
        logger.info(f"接收到文章列表，共 {len(all_articles)} 篇文章")

    # 尝试使用模组配置（基于文章URL进行配置优化）
    if use_module_config and USE_MODULE_MANAGER and all_articles:
        try:
            # 使用第一个文章的URL来匹配模组配置
            first_article_url = all_articles[0][1] if len(all_articles[0]) > 1 else None
            if first_article_url:
                module_config = get_config_for_url(first_article_url)
                if module_config:
                    module_name = match_module_for_url(first_article_url)
                    if log_callback:
                        log_callback(f"📦 使用模组配置: {module_name}")
                    else:
                        logger.info(f"使用模组配置: {module_name}")

                    # 只有模组配置中有相同字段时才覆盖，否则使用基础配置（传入参数）
                    if 'title_selectors' in module_config:
                        title_selectors = module_config['title_selectors']
                    if 'date_selectors' in module_config:
                        date_selectors = module_config['date_selectors']
                    if 'source_selectors' in module_config:
                        source_selectors = module_config['source_selectors']
                    if 'content_selectors' in module_config:
                        content_selectors = module_config['content_selectors']
                    if 'content_type' in module_config:
                        content_type = module_config['content_type']

                    # 更新其他设置
                    if 'mode' in module_config:
                        mode = module_config['mode']
                    if 'collect_links' in module_config:
                        collect_links = module_config['collect_links']
                    if 'retry' in module_config:
                        retry = module_config['retry']
                    if 'interval' in module_config:
                        interval = module_config['interval']
        except Exception as e:
            if log_callback:
                log_callback(f"⚠️ 使用模组配置失败，使用默认配置: {e}")
            else:
                logger.warning(f"使用模组配置失败，使用默认配置: {e}")

    # 处理字段配置 - 参考模组配置的逻辑
    if use_field_config:
        try:
            from .field_config_manager import get_field_config_manager

            manager = get_field_config_manager()
            field_configs = {}

            if field_preset:
                if log_callback:
                    log_callback(f"🔧 应用字段预设: {field_preset}")
                # 获取预设字段配置
                presets = manager.get_field_presets()
                if field_preset in presets:
                    field_names = presets[field_preset]
                    all_fields = manager.get_available_fields()
                    for field_name in field_names:
                        if field_name in all_fields:
                            field_configs[field_name] = all_fields[field_name]
                else:
                    if log_callback:
                        log_callback(f"⚠️ 未找到字段预设: {field_preset}")

            elif custom_field_list:
                if log_callback:
                    log_callback(f"🔧 应用自定义字段: {len(custom_field_list)} 个字段")
                # 获取自定义字段配置
                all_fields = manager.get_available_fields()
                for field_name in custom_field_list:
                    if field_name in all_fields:
                        field_configs[field_name] = all_fields[field_name]
                    else:
                        if log_callback:
                            log_callback(f"⚠️ 字段不存在: {field_name}")
            else:
                if log_callback:
                    log_callback("🔧 使用默认字段配置")
                # 使用基础字段配置
                default_fields = manager.config_data.get("default_fields", {})
                field_configs.update(default_fields)

            # 直接覆盖选择器变量 - 参考模组配置的做法
            if field_configs:
                if log_callback:
                    log_callback(f"🔧 字段配置覆盖选择器: {len(field_configs)} 个字段")

                # 只有字段配置中有相同字段时才覆盖，否则使用基础配置（传入参数）
                if 'title' in field_configs and 'selectors' in field_configs['title']:
                    title_selectors = field_configs['title']['selectors']
                    if log_callback:
                        log_callback(f"   📝 title: {len(title_selectors)} 个选择器")

                if 'content' in field_configs and 'selectors' in field_configs['content']:
                    content_selectors = field_configs['content']['selectors']
                    if log_callback:
                        log_callback(f"   📝 content: {len(content_selectors)} 个选择器")

                if 'dateget' in field_configs and 'selectors' in field_configs['dateget']:
                    date_selectors = field_configs['dateget']['selectors']
                    if log_callback:
                        log_callback(f"   📝 dateget: {len(date_selectors)} 个选择器")

                if 'source' in field_configs and 'selectors' in field_configs['source']:
                    source_selectors = field_configs['source']['selectors']
                    if log_callback:
                        log_callback(f"   📝 source: {len(source_selectors)} 个选择器")

        except ImportError:
            if log_callback:
                log_callback("⚠️ 字段配置功能不可用，使用默认字段")
        except Exception as e:
            if log_callback:
                log_callback(f"⚠️ 应用字段配置失败: {e}")

    # 确保选择器为列表格式
    if title_selectors is None:
        title_selectors = []
    elif isinstance(title_selectors, str):
        title_selectors = [title_selectors]

    if date_selectors is None:
        date_selectors = []
    elif isinstance(date_selectors, str):
        date_selectors = [date_selectors]

    if source_selectors is None:
        source_selectors = []
    elif isinstance(source_selectors, str):
        source_selectors = [source_selectors]

    # 统一的文章处理逻辑（专注于处理传入的 all_articles）
    # 首要任务：URL去重处理
    original_count = len(all_articles)
    all_articles = deduplicate_articles_by_url(all_articles, log_callback)
    deduplicated_count = len(all_articles)

    if log_callback:
        log_callback(f"🔄 URL去重完成: {original_count} → {deduplicated_count} 篇文章 (去除 {original_count - deduplicated_count} 个重复)")
    else:
        logger.info(f"URL去重完成: {original_count} → {deduplicated_count} 篇文章 (去除 {original_count - deduplicated_count} 个重复)")

    # 第二步：保存URL到缓存（确保所有路由到此函数的模块都能享受缓存功能）
    if config_group and all_articles:
        # 提取URL列表
        article_urls = []
        for article_info in all_articles:
            if len(article_info) > 1:  # 确保有URL字段
                article_urls.append(article_info[1])  # URL在索引1位置

        if article_urls:
            # 使用第一个文章URL作为基础URL来保存缓存
            base_cache_url = article_urls[0] if article_urls else "unknown"
            save_collected_urls_to_csv(article_urls, base_cache_url, config_group, log_callback)
            if log_callback:
                log_callback(f"💾 已保存 {len(article_urls)} 个URL到缓存 (配置组: {config_group or 'default'})")
            else:
                logger.info(f"已保存 {len(article_urls)} 个URL到缓存 (配置组: {config_group or 'default'})")

    # 使用批处理函数处理文章
    result = await process_articles_batch(
        all_articles=all_articles,
        content_selectors=content_selectors,
        title_selectors=title_selectors,
        date_selectors=date_selectors,
        source_selectors=source_selectors,
        content_type=content_type,
        collect_links=collect_links,
        mode=mode,
        filters=filters,
        export_filename=export_filename,
        classid=classid,
        file_format=file_format,
        excel_write_strategy=excel_write_strategy,
        retry=retry,
        interval=interval,
        max_workers=max_workers,
        log_callback=log_callback,
        progress_callback=progress_callback,
        use_module_config=use_module_config,
        # 传递字段配置参数
        field_preset=field_preset,
        custom_field_list=custom_field_list,
        user_custom_fields=user_custom_fields,
        use_field_config=use_field_config,
        # 传递浏览器配置参数
        headless=headless,
        disable_js_injection=disable_js_injection
    )

    return {
        "total": result['total_articles'],
        "success": result['processed_articles'],
        "failed": result['failed_articles'],
        "save_dir": result['save_dir']
    }



def debug_date_extraction(page_content, date_selectors, log_callback=None):
    """调试日期提取问题"""
    if log_callback:
        log_callback("=== 日期提取调试信息 ===")
        log_callback(f"页面内容长度: {len(page_content)}")
        log_callback(f"日期选择器: {date_selectors}")

        # 检查页面中是否包含常见的日期关键词
        date_keywords = ["时间", "日期", "发布", "publish", "date", "time"]
        for keyword in date_keywords:
            if keyword in page_content.lower():
                log_callback(f"页面包含关键词: {keyword}")

        # 查找页面中的时间相关元素
        import re
        time_patterns = [
            r'\d{4}[-/年]\d{1,2}[-/月]\d{1,2}[日]?',
            r'\d{1,2}[-/]\d{1,2}[-/]\d{4}',
            r'\d{4}年\d{1,2}月\d{1,2}日',
        ]

        for pattern in time_patterns:
            matches = re.findall(pattern, page_content)
            if matches:
                log_callback(f"找到时间模式 {pattern}: {matches[:3]}")  # 只显示前3个匹配